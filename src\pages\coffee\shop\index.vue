<template>
	<div>
		<Card :bordered="false" dis-hover>
			<PageHeader class="product_tabs" :title="$route.meta.title" hidden-breadcrumb>
				<div slot="content">
					<Tabs @on-click="onClickTab">
						<TabPane label="全部" name="all" />
						<TabPane label="营业中" name="0" />
						<TabPane label="已停业" name="1" />
					</Tabs>
				</div>
			</PageHeader>
		</Card>

		<Card :bordered="false" dis-hover class="ive-mt tablebox">
			<div class="btnbox">
				<div class="btns" @click="add">添加门店</div>
			</div>
			<div class="table">
				<Table
					:columns="columns"
					:data="orderList"
					ref="table"
					class="mt25"
					:loading="loading"
					highlight-row
					no-userFrom-text="暂无数据"
					no-filtered-userFrom-text="暂无筛选结果"
				>
					<template slot-scope="{ row, index }" slot="image">
						<img :src="row.image" />
					</template>
					<template slot-scope="{ row, index }" slot="action">
						<a @click="edit(row)">编辑</a>
						<Divider type="vertical" />
						<a @click="delte(row, '删除该门店', index)">删除</a>
					</template>
				</Table>
				<div class="acea-row row-right page">
					<Page
						:total="total"
						:current="formValidate.page"
						show-elevator
						show-total
						@on-change="pageChange"
						:page-size="formValidate.limit"
					/>
				</div>
			</div>
		</Card>
		<add-store ref="template"></add-store>
	</div>
</template>

<script>
import util from '@/libs/util';
import Setting from '@/setting';
import addStore from './addForm';
import { storeLogin, storeSetShowApi, resetApi } from '@/api/store'
import { shopListApi, shopDelApi } from '@/api/coffee';
export default {
	name: 'storeList',
	components: {
		addStore
	},
	data() {
		return {
			BaseURL: Setting.apiBaseURL.replace(/adminapi/, "store/home/"),
			total: 0,
			loading: false,
			formValidate: {
				type: 'all',
				page: 1,
				limit: 15,
			},
			columns: [
				{
					title: 'ID',
					key: 'ids',
					width: 60
				},
				{
					title: '门店图片',
					slot: 'image',
					minWidth: 80
				},
				{
					title: '门店名称',
					key: 'name',
					minWidth: 80
				},
				{
					title: '联系电话',
					key: 'phone',
					minWidth: 80
				},
				{
					title: '门店地址',
					key: 'address',
					ellipsis: true,
					minWidth: 150
				},
				{
					title: '营业时间',
					key: 'day_time',
					minWidth: 120
				},
				{
					title: '营业状态',
					key: 'status_name',
					minWidth: 80
				},
				{
					title: '操作',
					slot: 'action',
					fixed: 'right',
					minWidth: 220,
					align: 'center'
				}
			],
			orderList: [

			]
		}
	},
	mounted() {
		this.getList()
	},
	methods: {
		getList() {
			this.loading = true
			shopListApi(this.formValidate).then(res => {
				this.orderList = res.data.list
				this.total = res.data.count
				this.loading = false
			})
		},
		reset(row) {
			this.$modalForm(resetApi(row.id)).then(() => this.getList());
		},
		add() {
			this.$refs.template.title = '添加门店';
			this.$refs.template.add = 1;
			this.$refs.template.isTemplate = true;
		},
		edit(row) {
			this.$refs.template.title = '编辑门店'
			this.$refs.template.isTemplate = true;
			this.$refs.template.getInfo(row.id);
		},
		getExpiresTime(expiresTime) {
			let nowTimeNum = Math.round(new Date() / 1000);
			let expiresTimeNum = expiresTime - nowTimeNum;
			return parseFloat(parseFloat(parseFloat(expiresTimeNum / 60) / 60) / 24);
		},
		delte(row, tit, num) {
			this.$Modal.confirm({
				title: '确定要删除该门店吗？',
				content: '删除该门店后将无法恢复，请谨慎操作！',
				loading: true,
				onOk: () => {
					shopDelApi({'id':row.id}).then(res=>{
						this.$Message.success(res.msg);
						this.getList()
						this.$Modal.remove();
					})
				}
			});
		},
		onClickTab(e) {
			this.formValidate.page = 1
			this.formValidate.type = e
			this.getList()
		},
		//分页
		pageChange(status) {
			this.formValidate.page = status;
			this.getList()
		}
	},
}
</script>

<style scoped lang="less">
/deep/.ivu-page-header,
/deep/.ivu-tabs-bar {
	border-bottom: 1px solid #ffffff;
}
/deep/.ivu-card-body {
	padding: 0;
}
/deep/.ivu-tabs-nav {
	height: 45px;
}
.tablebox {
	margin-top: 15px;
}
.btnbox {
	padding: 20px 0px 0px 30px;
	.btns {
		width: 99px;
		height: 32px;
		background: #1890ff;
		border-radius: 4px;
		text-align: center;
		line-height: 32px;
		color: #ffffff;
		cursor: pointer;
	}
}
.table {
	padding: 0px 30px 15px 30px;
	img {
		width: 40px;
		height: 40px;
	}
}
.search {
	width: 86px;
	height: 32px;
	background: #1890ff;
	border-radius: 4px;
	text-align: center;
	line-height: 32px;
	font-size: 13px;
	font-family: PingFangSC-Regular, PingFang SC;
	font-weight: 400;
	color: #ffffff;
	cursor: pointer;
}
.reset {
	width: 86px;
	height: 32px;
	border-radius: 4px;
	border: 1px solid rgba(151, 151, 151, 0.36);
	text-align: center;
	line-height: 32px;
	font-size: 13px;
	font-family: PingFangSC-Regular, PingFang SC;
	font-weight: 400;
	color: rgba(0, 0, 0, 0.85);
	cursor: pointer;
}
</style>
