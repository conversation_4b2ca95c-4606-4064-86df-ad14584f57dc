<template>
    <div>
        <div class="i-layout-page-header">
            <PageHeader class="product_tabs" title="订单管理" hidden-breadcrumb>
                <div slot="content">
                    <Tabs v-model="currentTab" @on-click="onClickTab" v-if="tablists">
                        <TabPane :label=" '全部订单（'+tablists.orderTotal+'）'" name=" "/>
                        <TabPane :label=" '微信订单（'+tablists.weixinPay+'）'" name="1"/>
                        <TabPane :label=" '园区卡订单（'+tablists.cardPay+'）'" name="2"/>
                        <TabPane :label=" '优惠券订单（'+tablists.couponPay+'）'" name="3"/>
                    </Tabs>
                </div>
            </PageHeader>
        </div>
        <productlist-details v-if="currentTab === 'article' || 'project' || 'app'" ref="productlist" ></productlist-details>
        <Spin size="large" fix v-if="spinShow"> </Spin>
    </div>
</template>

<script>
    import productlistDetails from './orderlistDetails';
    import { mapMutations } from 'vuex';
    export default {
        name: 'list',
        components: {
            productlistDetails
        },
        data () {
            return {
                spinShow: false,
                currentTab: '',
                data: [],
                tablists: null
            }
        },
        created () {
            this.getOrderType('');
            this.getOrderStatus('');
            this.getOrderTime('');
            this.getOrderNum('');
            this.getfieldKey('');
            this.onChangeTabs('');
            this.getOrderSelf('');
        },
        beforeDestroy () {
            this.getOrderType('');
            this.getOrderStatus('');
            this.getOrderTime('');
            this.getOrderNum('');
            this.getfieldKey('');
            this.onChangeTabs('');
            this.getOrderSelf('');
        },
        mounted () {
            this.getTabs()
        },
        methods: {
            ...mapMutations('admin/order', [
                'getOrderType',
                'onChangeTabs',
                'getOrderStatus',
                'getOrderTime',
                'getOrderNum',
                'getfieldKey',
                'getOrderTabs_v2',
                'getOrderSelf',
            ]),
            // 订单类型  @on-changeTabs="getChangeTabs"
            getTabs () {
                this.spinShow = true;
                this.$store.dispatch('admin/order/getOrderTabs_v2', {
                    data: ''
                }).then(res => {
                    this.tablists = res.data;
                    this.spinShow = false;
                }).catch(res => {
                    this.spinShow = false;
                    this.$Message.error(res.msg);
                })
            },
            onClickTab () {
                this.onChangeTabs(Number(this.currentTab))
                this.$store.dispatch('admin/order/getOrderTabs_v2', { type: this.currentTab });
            }
        }
    }
</script>
<style scoped lang="stylus">
    .product_tabs >>> .ivu-tabs-bar
      margin-bottom 0px !important
    .product_tabs >>> .ivu-page-header-content
      margin-bottom 0px !important
    .product_tabs >>> .ivu-page-header-breadcrumb
        margin-bottom 0px !important
</style>
