<style lang="less">
	.ivu-modal{
		top: 0;
	}
</style>
<template>
	<div>
		<Modal v-model="modals" footer-hide title="商品规格" :mask-closable="false" width="600">
			<div class="productAttr">
				<div class="acea-row">
					<div class="pictrue">
						<img :src="attr.img" />
					</div>
					<div class="text">
						<div class="name line1">{{ attr.name }}</div>
						<div class="money">
							¥
							<span class="num">{{ pay_price }}</span>
						</div>
						<div class="attr">
							<div class="list" v-for="(item, indexw) in attr.attrs" :key="indexw">
								<div class="title">{{ item.value }}</div>
								<div class="listn acea-row">
									<div
										class="item acea-row row-center-wrapper"
										:class="item.selected == indexn ? 'on' : ''"
										v-for="(itemn, indexn) in item.detail"
										@click="tapAttr(indexw, indexn, item.value)"
									>{{ itemn }}</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<button
					type="primary"
					:disabled="disabled"
					class="bnt acea-row row-center-wrapper"
					@click="createOrder"
				>确定</button>
			</div>
		</Modal>
	</div>
</template>

<script>
import {
	cashierCreateOrder,
} from '@/api/coffee';
import { forEach } from '../../../../libs/tools';
export default {
	name: 'productAttr',
	props: {
		attr: {
			type: Object,
			default: () => { }
		},
		isCart: {
			type: Number,
			value: 0
		},
		disabled: {
			type: Boolean,
			value: false
		}
	},
	data() {
		return {
			modals: false,
			pay_price: 0,
			data: [],
		}
	},
	methods: {
		info(data) {
			this.pay_price = data.price;
			this.data = [];
		},
		createOrder: function () {
			let data = this.data;
			let arr = [];
			for (let i in data) {
				arr.push(JSON.stringify(data[i]))
			}
			cashierCreateOrder({ product_id: this.attr.id, attr: arr, price: this.pay_price, shop_id: this.attr.shop_id }).then(res => {
				this.$emit("getCartList");
				this.$Message.success(res.msg)
				this.modals = false;
			}).catch(err => {
				this.$Message.error(err.msg)
			})
		},
		tapAttr: function (indexw, indexn, value) {
			let that = this;
			let selected = that.attr.attrs[indexw].selected
			let price = that.attr.attrs[indexw].detail[indexn];
			let attr = price;
			price = price.substr(price.indexOf("¥") + 1, (price.indexOf(")") - price.indexOf("¥") - 1));
			attr = attr.substr(0, attr.indexOf("("));
			if (selected != -1) {
				if (selected == indexn) {
					let attrPrice = Number(that.pay_price) - that.data[value].price
					that.pay_price = attrPrice.toFixed(2);
					that.attr.attrs[indexw].selected = -1;
					delete that.data[value];
				} else {
					let attrPrice = Number(that.pay_price) - that.data[value].price
					attrPrice = attrPrice + Number(price);
					that.pay_price = attrPrice.toFixed(2);
					let res = { 'name': attr, 'price': Number(price) };
					that.data[value] = res
					that.attr.attrs[indexw].selected = indexn;
				}
			} else {
				if (price > 0) {
					let attrPrice = Number(that.pay_price) + Number(price);
					that.pay_price = attrPrice.toFixed(2);
				}
				let res = { 'name': attr, 'price': Number(price) };
				that.data[value] = res
				that.attr.attrs[indexw].selected = indexn;
			}
		},
		//获取被选中属性；
		getCheckedValue: function () {
			let productAttr = this.attr.productAttr;
			let value = [];
			for (let i = 0; i < productAttr.length; i++) {
				for (let j = 0; j < productAttr[i].attr_values.length; j++) {
					if (productAttr[i].index === productAttr[i].attr_values[j]) {
						value.push(productAttr[i].attr_values[j]);
					}
				}
			}
			return value;
		}
	}
}
</script>

<style lang="stylus" scoped>
	::-webkit-scrollbar-thumb {
		-webkit-box-shadow: inset 0 0 6px #eee;
	}

	::-webkit-scrollbar {
		width: 4px !important;
		/*对垂直流动条有效*/
	}

	.productAttr {
		.pictrue {
			width: 180px;
			height: 180px;
			margin-right: 20px;

			img {
				width: 100%;
				height: 100%;
			}
		}

		.bnt {
			width: 550px;
			height: 36px;
			background: #1890FF;
			font-size: 12px;
			color: #fff;
			border-radius: 4px;
			margin: 20px auto 0 auto;
			cursor: pointer;
			border: none;
		}

		.text {
			width: 365px;

			.attr {
				height: 300px;
				overflow-x: hidden;
				overflow-y: scroll;

			}

			.list {
				.title {
					color: rgba(0, 0, 0, 0.85);
					font-size: 14px;
					font-weight: 500;
					margin-top: 14px;
				}

				.listn {
					.item {
						min-width: 158px;
						height: 35px;
						border: 1px solid #EEEEEE;
						margin-top: 10px;
						margin-right: 10px;
						cursor: pointer;
						padding: 0 6px;

						&.on {
							background-color: #1890FF;
							color: #fff;
						}
					}
				}
			}

			.name {
				color: #000;
				font-size: 18px;
				font-weight: 400;
			}

			.info {
				font-size: 13px;
				color: #999999;
				margin: 8px 0 6px 0;
			}

			.money {
				font-size: 14px;
				color: #F5222D;
				border-bottom: 1px solid #eee;
				width: 100%;
				padding-bottom: 13px;

				.num {
					font-size: 21px;
				}
			}
		}
	}
</style>
