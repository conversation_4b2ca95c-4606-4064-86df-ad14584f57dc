<template>
  <div>
    <Card :bordered="false" dis-hover class="ivu-mt listbox">
      <Form
        ref="userFrom"
        :model="userFrom"
        :label-width="labelWidth"
        :label-position="labelPosition"
        @submit.native.prevent
      >
        <Row :gutter="16">
          <Col span="18">
            <Row>
              <Col span="24">
                <Row>
                  <Col v-bind="grid">
                    <FormItem label="用户搜索：" label-for="phone">
                      <Input
                        v-model="userFrom.phone"
                        placeholder="请输入手机号"
                        element-id="phone"
                        clearable
                      >
                      </Input>
                    </FormItem>
                  </Col>
                </Row>
                              <Row>
                <Col v-bind="grid">
                  <FormItem label="身份：" label-for="is_promoter">
                    <RadioGroup v-model="userFrom.is_promoter" type="button">
                      <Radio label="">
                        <span>全部</span>
                      </Radio>
                      <Radio label="2">
                        <span>店员</span>
                      </Radio>
                      <Radio label="1">
                        <span>普通用户</span>
                      </Radio>
                    </RadioGroup>
                  </FormItem>
                </Col>
              </Row>
              </Col>
            </Row>
          </Col>

          <Col span="6" class="ivu-text-right userFrom">
            <FormItem>
              <Button
                type="primary"
                icon="ios-search"
                label="default"
                class="mr15"
                @click="userSearchs"
                >搜索</Button
              >
              <Button class="ResetSearch" @click="reset('userFrom')"
                >重置</Button
              >
            </FormItem>
          </Col>
        </Row>
      </Form>
      <Table
        :columns="columns"
        :data="userLists"
        class="mt25"
        ref="table"
        highlight-row
        :loading="loading"
        no-userFrom-text="暂无数据"
        no-filtered-userFrom-text="暂无筛选结果"
        @on-sort-change="sortChanged"
        @on-select-all="selectAll"
        @on-select-all-cancel="selectAll"
        @on-select-cancel="onSelectCancel"
      >
        <template slot-scope="{ row, index }" slot="avatars">
          <viewer>
            <div class="tabBox_img">
              <img v-lazy="row.headimgurl" />
            </div>
          </viewer>
        </template>
        <template slot-scope="{ row, index }" slot="nickname">
          <div class="acea-row">
            <Icon
              type="md-male"
              v-show="row.sex == 1"
              color="#2db7f5"
              size="15"
              class="mr5"
            />
            <Icon
              type="md-female"
              v-show="row.sex == 2"
              color="#ed4014"
              size="15"
              class="mr5"
            />
            <div v-text="row.nickname"></div>
          </div>
        </template>
        <template slot-scope="{ row, index }" slot="isMember">
          <div>{{ row.is_role == 4 ? "是" : "否" }}</div>
        </template>
        <template slot-scope="{ row, index }" slot="action">
          <a @click="edit(row)">编辑</a>
          <Divider type="vertical" />
          <template>
            <Dropdown @on-click="changeMenu(row, $event, index)">
              <a href="javascript:void(0)">
                更多
                <Icon type="ios-arrow-down"></Icon>
              </a>
              <DropdownMenu slot="list">
                <DropdownItem name="1">用户详情</DropdownItem>
                <DropdownItem name="2">余额修改</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
        </template>
      </Table>
      <div class="acea-row row-right page">
        <Page
          :total="total"
          :current="userFrom.page"
          show-elevator
          show-total
          @on-change="pageChange"
          :page-size="userFrom.limit"
        />
      </div>
    </Card>
    <!-- 编辑表单 积分余额-->
    <edit-from
      ref="edits"
      :FromData="FromData"
      @submitFail="submitFail"
    ></edit-from>
    <!-- 会员详情-->
    <user-details ref="userDetails"></user-details>
  </div>
</template>

<script>
import userLabel from "../../../components/userLabel";
import { mapState } from "vuex";
import expandRow from "./tableExpand.vue";
import {
  isShowApi,
  getUserSaveForm,
} from "@/api/user";
import { userList,getUserData,editOtherApi } from "@/api/air";
import { agentSpreadApi } from "@/api/agent";
import editFrom from "../../../components/from/from";
import sendFrom from "@/components/sendCoupons/index";
import userDetails from "./handle/userDetails";
import newsCategory from "@/components/newsCategory/index";
import city from "@/utils/city";
import customerInfo from "@/components/customerInfo";
export default {
  name: "user_list",
  components: {
    expandRow,
    editFrom,
    sendFrom,
    userDetails,
    newsCategory,
    customerInfo,
    userLabel,
  },
  data() {
    return {
      labelShow: false,
      customerShow: false,
      promoterShow: false,
      labelActive: {
        uid: 0,
      },
      formInline: {
        uid: 0,
        spread_uid: 0,
        image: "",
      },
      collapse: false,
      address: [],
      addresData: city,
      isShowSend: true,
      modal13: false,
      maxCols: 4,
      scrollerHeight: "600",
      contentTop: "130",
      contentWidth: "98%",
      grid: {
        xl: 8,
        lg: 8,
        md: 12,
        sm: 24,
        xs: 24,
      },
      grid2: {
        xl: 18,
        lg: 16,
        md: 12,
        sm: 24,
        xs: 24,
      },
      loading: false,
      total: 0,
      userFrom: {
        is_promoter:'',
        phone:'',
        page: 1,
        limit: 15,
      },
      field_key: "",
      level: "",
      group_id: "",
      label_id: "",
      user_time_type: "",
      pay_count: "",
      columns: [
        {
          title: "头像",
          slot: "avatars",
          minWidth: 60,
        },
        {
          title: "昵称",
          slot: "nickname",
          minWidth: 120,
        },
        {
          title: "店员",
          slot: "isMember",
          minWidth: 40,
        },
        {
          title: "手机号",
          key: "phone",
          minWidth: 100,
        },
        {
          title: "余额",
          key: "money",
          sortable: "custom",
          minWidth: 100,
        },
        {
          title: "消费次数",
          key: "consume_count",
          minWidth: 50,
        },
        {
          title: "消费金额",
          key: "consume_money",
          minWidth: 50,
        },
        {
          title: "创建时间",
          key: "create_time",
          minWidth: 150,
        },
        {
          title: "操作",
          slot: "action",
          fixed: "right",
          minWidth: 120,
        },
      ],
      userLists: [],
      FromData: null,
      selectionList: [],
      user_ids: "",
      selectedData: [],
      timeVal: [],
      array_ids: [],
      groupList: [],
      levelList: [],
      labelFrom: {
        page: 1,
        limit: "",
      },
      labelLists: [],
      display: "none",
      checkBox: false,
      selectionCopy: [],
      isAll: -1,
    };
  },
  watch: {
    selectionList(value) {
      let arr = value.map((item) => item.uid);
      this.array_ids = arr;
      this.user_ids = arr.join();
    },
    userLists: {
      deep: true,
      handler(value) {
        value.forEach((item) => {
          this.selectionList.forEach((itm) => {
            if (itm.uid === item.uid) {
              item.checkBox = true;
            }
          });
        });
        const arr = this.userLists.filter((item) => item.checkBox);
        if (this.userLists.length) {
          this.checkBox = this.userLists.length === arr.length;
        } else {
          this.checkBox = false;
        }
      },
    },
  },
  computed: {
    ...mapState("admin/layout", ["isMobile"]),
    labelWidth() {
      return this.isMobile ? undefined : 100;
    },
    labelPosition() {
      return this.isMobile ? "top" : "right";
    },
  },
  created() {
    this.getList();
  },
  mounted() {
  },
  methods: {
    // 会员列表
    getList() {
      this.loading = true;
      this.userFrom.phone = this.userFrom.phone || "";
      this.userFrom.is_promoter = this.userFrom.is_promoter || "";
      userList(this.userFrom)
        .then(async (res) => {
          let data = res.data;
          this.userLists = data.list
          this.total = data.count;
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
          this.$Message.error(res.msg);
        });
    },
    selectAll(row) {
      if (row.length) {
        this.selectionList = row;
        this.selectionCopy = row;
      }
      this.selectionCopy.forEach((item, index) => {
        item.checkBox = this.checkBox;
        this.$set(this.userLists, index, item);
      });
    },
    // 提交
    putSend(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (!this.formInline.spread_uid) {
            return this.$Message.error("请上传用户");
          }
          agentSpreadApi(this.formInline)
            .then((res) => {
              this.promoterShow = false;
              this.$Message.success(res.msg);
              this.getList();
              this.$refs[name].resetFields();
            })
            .catch((res) => {
              this.$Message.error(res.msg);
            });
        }
      });
    },
    save() {
      this.$modalForm(getUserSaveForm()).then(() => this.getList());
    },
    // 操作
    changeMenu(row, name, index) {
      switch (name) {
        case "1":
          this.$refs.userDetails.modals = true;
          this.$refs.userDetails.getDetails(row.id);
          break;
        case "2":
          this.getOtherFrom(row.id);
          break;
        default:
          break;
      }
    },
    openLabel(row) {
      this.labelShow = true;
      this.labelActive.uid = row.uid;
    },
    customer() {
      this.customerShow = true;
    },
    imageObject(e) {
      this.customerShow = false;
      this.formInline.spread_uid = e.uid;
      this.formInline.image = e.image;
    },
    cancel(name) {
      this.promoterShow = false;
      this.$refs[name].resetFields();
    },
    // 删除
    del(row, tit, num, name) {
      let delfromData = {
        title: tit,
        num: num,
        url:
          name === "user"
            ? `user/del_level/${row.uid}`
            : `agent/stair/delete_spread/${row.uid}`,
        method: name === "user" ? "DELETE" : "PUT",
        // url: `user/del_level/${row.uid}`,
        // method: 'DELETE',
        ids: "",
      };
      this.$modalSure(delfromData)
        .then((res) => {
          this.$Message.success(res.msg);
          this.getList();
        })
        .catch((res) => {
          this.$Message.error(res.msg);
        });
    },
    // 清除会员删除成功
    submitModel() {
      this.getList();
    },
    pageChange(index) {
      this.userFrom.page = index;
      this.getList();
    },
    // 搜索
    userSearchs() {
      this.userFrom.page = 1;
      this.selectionList = [];
      this.getList();
    },
    // 重置
    reset(name) {
      this.userFrom = {
        phone: "",
        is_promoter: "",
        page: 1, // 当前页
        limit: 20, // 每页显示条数
      };
      this.getList();
    },
    // 获取编辑表单数据
    getUserFrom(id) {
      getUserData({id:id})
        .then(async (res) => {
          if (res.data.status === false) {
            return this.$authLapse(res.data);
          }
          this.FromData = res.data;
          this.$refs.edits.modals = true;
        })
        .catch((res) => {
          this.$Message.error(res.msg);
        });
    },
    // 获取积分余额表单
    getOtherFrom(id) {
      editOtherApi({id:id})
        .then(async (res) => {
          if (res.data.status === false) {
            return this.$authLapse(res.data);
          }
          this.FromData = res.data;
          this.$refs.edits.modals = true;
        })
        .catch((res) => {
          this.$Message.error(res.msg);
        });
    },
    // 修改状态
    onchangeIsShow(row) {
      let data = {
        id: row.uid,
        status: row.status,
      };
      isShowApi(data)
        .then(async (res) => {
          this.$Message.success(res.msg);
        })
        .catch((res) => {
          this.$Message.error(res.msg);
        });
    },
    // 编辑
    edit(row) {
      this.getUserFrom(row.id);
    },
    // 修改成功
    submitFail() {
      // this.getList();
    },
    // 排序
    sortChanged(e) {
      this.userFrom[e.key] = e.order;
      this.getList();
    },
    onSelectCancel(selection, row) {},
  },
};
</script>

<style scoped lang="stylus">
.picBox {
  display: inline-block;
  cursor: pointer;

  .upLoad {
    width: 58px;
    height: 58px;
    line-height: 58px;
    border: 1px dotted rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.02);
  }

  .pictrue {
    width: 60px;
    height: 60px;
    border: 1px dotted rgba(0, 0, 0, 0.1);
    margin-right: 10px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.userFrom {
  >>> .ivu-form-item-content {
    margin-left: 0px !important;
  }
}

.userAlert {
  margin-top: 20px;
}

.userI {
  color: #1890FF;
  font-style: normal;
}

img {
  height: 36px;
  display: block;
}

.tabBox_img {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }
}

.tabBox_tit {
  width: 60%;
  font-size: 12px !important;
  margin: 0 2px 0 10px;
  letter-spacing: 1px;
  padding: 5px 0;
  box-sizing: border-box;
}

.modelBox {
  >>> .ivu-modal-body {
    padding: 0 16px 16px 16px !important;
  }
}

.vipName {
  color: #dab176;
}

.listbox {
  >>>.ivu-divider-horizontal {
    margin: 0 !important;
  }
}

/deep/.ivu-table-header {
  // overflow visible
}

/deep/.ivu-table th {
  overflow: visible;
}

/deep/.select-item:hover {
  background-color: #f3f3f3;
}

/deep/.select-on {
  display: block;
}

/deep/.select-item.on {
  /*background: #f3f3f3;*/
}
</style>
