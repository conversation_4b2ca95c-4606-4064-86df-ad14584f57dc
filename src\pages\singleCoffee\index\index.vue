<template>
  <div>
    <div class="i-layout-page-header">
      <PageHeader
        class="product_tabs"
        :title="$store.state.admin.coffee.shopName + $store.state.admin.coffee.cateName + $store.state.admin.coffee.productName"
        hidden-breadcrumb
      ></PageHeader>
    </div>
    <to-day ref="header"></to-day>
    <transaction-from ref="tran"></transaction-from>
  </div>
</template>

<script>
import toDay from "./components/toDay";
import transactionFrom from "./components/transaction";
import { mapMutations } from "vuex";
export default {
  name: "index",
  components: {
    toDay,
    transactionFrom,
  },
  created() {
    // 检测用户权限跳转
    let role = this.$store.state.admin.user.info.role
    if(role == 7) {
      this.$router.push({ path: "/admin/singleCoffee/index" });
    }
  },
  data() {
    return {
      shopName: "",
      cateName: "",
    };
  },
  methods: {
    ...mapMutations("admin/coffee", [
      "setShopName",
      "setCateName",
    ]),
  },
};
</script>

<style scoped></style>
