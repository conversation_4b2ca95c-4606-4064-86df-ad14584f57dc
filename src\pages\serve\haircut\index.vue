<template>
  <div class="article-manager">
    <div class="i-layout-page-header">
      <PageHeader title="理发服务" hidden-breadcrumb></PageHeader>
    </div>
    <Card :bordered="false" dis-hover class="ivu-mt">
      <Form
        ref="orderData"
        :model="orderData"
        :label-width="labelWidth"
        :label-position="labelPosition"
        class="tabform"
        @submit.native.prevent
      >
        <Row :gutter="24" type="flex" justify="end">
          <Col span="24" class="ivu-text-left">
            <FormItem label="订单状态：">
              <RadioGroup
                v-model="orderData.status"
                type="button"
                @on-change="selectChange2(orderData.status)"
              >
                <Radio label>全部</Radio>
                <Radio label="0">已预约</Radio>
                <Radio label="1">已完成</Radio>
                <Radio label="2">已取消</Radio>
              </RadioGroup>
            </FormItem>
          </Col>
          <Col span="24" class="ivu-text-left">
            <FormItem label="预约时间：">
              <DatePicker
                :editable="false"
                @on-change="onchangeTime"
                :value="timeVal"
                format="yyyy/MM/dd HH:mm:ss"
                type="datetimerange"
                placement="bottom-start"
                placeholder="自定义时间"
                style="width: 300px"
                class="mr20"
                :options="options"
              ></DatePicker>
            </FormItem>
          </Col>
          <Col span="24">
            <Col v-bind="grid" class="mr">
              <FormItem label="搜索：" prop="real_name" label-for="real_name">
                <Input
                  v-model="orderData.real_name"
                  search
                  enter-button
                  placeholder="请输入"
                  element-id="name"
                  @on-search="orderSearch(orderData.real_name)"
                >
                  <Select
                    v-model="orderData.field_key"
                    slot="prepend"
                    style="width: 80px"
                  >
                    <Option value="all">电话</Option>
                  </Select>
                </Input>
              </FormItem>
            </Col>
            <Col span="24">
              <Button type="primary" class="mr10" @click="exportData(1)"
                >导出预约记录</Button
              >
            </Col>
          </Col>
        </Row>
      </Form>
      <Table
        :columns="columns"
        :data="orderList"
        ref="table"
        :loading="loading"
        highlight-row
        no-data-text="暂无数据"
        no-filtered-data-text="暂无筛选结果"
        class="orderData mt25"
      >
        <template slot-scope="{ row, index }" slot="action">
          <a @click="edit(row)">修改状态</a>
        </template>
      </Table>
      <div class="acea-row row-right page">
        <Page
          :total="page.total"
          :current="page.pageNum"
          show-elevator
          show-total
          @on-change="pageChange"
          :page-size="page.pageSize"
          @on-page-size-change="limitChange"
          show-sizer
        />
      </div>
    </Card>
    <!-- 添加 编辑表单-->
    <add-form ref="template"></add-form>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { hairService } from "@/api/serve";
import addForm from "./addForm";
export default {
  name: "product_productClassify",
  components: {
    addForm,
  },
  data() {
    return {
      page: {
        total: 0, // 总条数
        pageNum: 1, // 当前页
        pageSize: 10, // 每页显示条数
      },
      grid: {
        xl: 8,
        lg: 8,
        md: 8,
        sm: 24,
        xs: 24,
      },
      orderList: [],
      loading: false,
      columns: [
        {
          title: "理发时间",
          align: "center",
          key: "time",
          minWidth: 150,
        },
        {
          title: "理发地点",
          align: "center",
          key: "address",
          minWidth: 150,
        },
        {
          title: "理发套餐",
          align: "center",
          key: "price",
          minWidth: 150,
        },
        {
          title: "性别",
          align: "center",
          key: "sex",
          minWidth: 30,
        },
        {
          title: "手机号",
          align: "center",
          key: "phone",
          minWidth: 150,
        },
        {
          title: "状态",
          align: "center",
          key: "status",
          minWidth: 80,
        },
        {
          title: "创建时间",
          align: "center",
          key: "create_time",
          minWidth: 150,
        },
        {
          title: "操作",
          slot: "action",
          fixed: "right",
          minWidth: 120,
          align: "center",
        },
      ],
      // 搜索条件
      orderData: {
        status: "",
        data: "",
        real_name: "",
        field_key: "all",
        pay_type: "",
        is_self: "",
      },
      // 时间
      fromList: {
        title: "选择时间",
        custom: true,
        fromTxt: [
          { text: "全部", val: "" },
          { text: "今天", val: "today" },
          { text: "昨天", val: "yesterday" },
          { text: "最近7天", val: "lately7" },
          { text: "最近30天", val: "lately30" },
          { text: "本月", val: "month" },
          { text: "本年", val: "year" },
        ],
      },
      timeVal: [],
      options: {
        shortcuts: [
          {
            text: "今天",
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(
                new Date(
                  new Date().getFullYear(),
                  new Date().getMonth(),
                  new Date().getDate()
                )
              );
              return [start, end];
            },
          },
          {
            text: "昨天",
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(
                start.setTime(
                  new Date(
                    new Date().getFullYear(),
                    new Date().getMonth(),
                    new Date().getDate() - 1
                  )
                )
              );
              end.setTime(
                end.setTime(
                  new Date(
                    new Date().getFullYear(),
                    new Date().getMonth(),
                    new Date().getDate()
                  ) - 1
                )
              );
              return [start, end];
            },
          },
          {
            text: "最近7天",
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(
                start.setTime(
                  new Date(
                    new Date().getFullYear(),
                    new Date().getMonth(),
                    new Date().getDate() - 6
                  )
                )
              );
              return [start, end];
            },
          },
          {
            text: "最近30天",
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(
                start.setTime(
                  new Date(
                    new Date().getFullYear(),
                    new Date().getMonth(),
                    new Date().getDate() - 29
                  )
                )
              );
              return [start, end];
            },
          },
          {
            text: "本月",
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(
                start.setTime(
                  new Date(new Date().getFullYear(), new Date().getMonth(), 1)
                )
              );
              return [start, end];
            },
          },
          {
            text: "本年",
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(
                start.setTime(new Date(new Date().getFullYear(), 0, 1))
              );
              return [start, end];
            },
          },
        ],
      },
    };
  },
  computed: {
    ...mapState("admin/layout", ["isMobile"]),
    ...mapState("admin/userLevel", ["categoryId"]),
    labelWidth() {
      return this.isMobile ? undefined : 75;
    },
    labelPosition() {
      return this.isMobile ? "top" : "right";
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 导出预约表
    exportData() {
      const columns = this.columns.slice(0, 7);
      hairService({
        page: 1,
        limit: 99999,
        where: this.orderData,
      }).then(async (res) => {
        let data = res.data.list;
        console.log(data)
        this.$refs.table.exportCsv({
          filename: "预约记录",
          columns: columns,
          data: data,
        });
      }).catch((res) => {
        this.loading = false;
        this.$Message.error(res.msg);
       });

    },
    selectChange2() {
      this.getList();
    },
    // 订单号搜索
    orderSearch() {
      this.getList();
    },
    limitChange(limit) {
      this.page.pageSize = limit;
      this.getList();
    },
    // 新
    onchangeTime(e) {
      if (e[1].slice(-8) === "00:00:00") {
        e[1] = e[1].slice(0, -8) + "23:59:59";
        this.timeVal = e;
      } else {
        this.timeVal = e;
      }
      this.orderData.data = this.timeVal[0] ? this.timeVal.join("-") : "";
    },
    // 列表
    getList() {
      hairService({
        page: this.page.pageNum,
        limit: this.page.pageSize,
        where: this.orderData,
      })
        .then(async (res) => {
          let data = res.data;
          this.orderList = data.list;
          this.page.total = data.count;
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
          this.$Message.error(res.msg);
        });
    },
    pageChange(index) {
      this.page.pageNum = index;
      this.getList();
    },
    // 编辑
    edit(row) {
      this.$refs.template.title = "编辑状态";
      this.$refs.template.isTemplate = true;
      this.$refs.template.getInfo(row.id);
    },
  },
};
</script>
<style scoped lang="stylus">
.treeSel >>>.ivu-select-dropdown-list
    padding 0 10px!important
    box-sizing border-box
.tabBox_img
    width 36px
    height 36px
    border-radius:4px
    cursor pointer
    img
        width 100%
        height 100%
>>>.ivu-select,.ivu-select-item,.vxe-table
    font-size 12px !important
/deep/.ivu-input
    font-size 14px !important
</style>

<style>
/*.ivu-input{*/
/*    font-size: 14px !important;*/
/*}*/
</style>
