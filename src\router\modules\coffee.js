// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import BasicLayout from "@/layouts/basic-layout";

const pre = "coffee_"; // 前缀

export default {
    path: "/admin/coffee",
    name: "coffee", // 当前路由的name
    header: "product",
    meta: {
        // 授权标识
        auth: ["admin-store-index"],
    },
    redirect: {
        name: `${pre}productList`,
    },
    component: BasicLayout,
    children: [
        {
            path: "shop", // redirect path
            name: `${pre}productList`,
            meta: {
                title: "店铺管理",
                auth: ["admin-store-storeProuduct-index"],
            },
            component: () => import("@/pages/coffee/shop"),
        },
        {
            path: "product_classify",
            name: `${pre}productClassify`,
            meta: {
                title: "商品分类",
                auth: ["admin-store-storeCategory-index"],
            },
            component: () => import("@/pages/coffee/category"),
        },
        {
            path: "product_list", // redirect path
            name: `${pre}productList`,
            meta: {
                title: "商品管理",
                auth: ["admin-store-storeProuduct-index"],
            },
            component: () => import("@/pages/coffee/product"),
        },
        {
            path: "add_product/:id?",
            name: `${pre}productAdd`,
            meta: {
                auth: ["admin-store-storeProuduct-index"],
                title: "商品添加",
            },
            component: () => import("@/pages/coffee/product/addForm"),
        },
        {
            path: "product_attr",
            name: `${pre}productAttr`,
            meta: {
                auth: ["admin-store-storeProuduct-index"],
                title: "商品规格",
            },
            component: () => import("@/pages/coffee/productAttr"),
        },
        {
            path: "coupon",
            name: `${pre}productCoupon`,
            meta: {
                auth: ["admin-store-storeProuduct-index"],
                title: "用户优惠券",
            },
            component: () => import("@/pages/coffee/coupon"),
        },
        {
            path: "couponShop",
            name: `${pre}productCouponShop`,
            meta: {
                auth: ["admin-store-storeProuduct-index"],
                title: "优惠券列表",
            },
            component: () => import("@/pages/coffee/couponShop"),
        },
        {
            path: "order",
            name: `${pre}productOrder`,
            meta: {
                auth: ["admin-store-storeProuduct-index"],
                title: "订单管理",
            },
            component: () => import("@/pages/coffee/order"),
        },
        {
            path: "transaction",
            name: `${pre}productTransaction`,
            meta: {
                auth: ["admin-store-storeProuduct-index"],
                title: "交易统计",
            },
            component: () => import("@/pages/coffee/transaction"),
        },
        {
            path: "user",
            name: `${pre}user`,
            meta: {
                auth: ["admin-store-storeProuduct-index"],
                title: "用户管理",
            },
            component: () => import("@/pages/coffee/user"),
        },
        {
            path: "finance",
            name: `${pre}finance`,
            meta: {
                auth: ["admin-store-storeProuduct-index"],
                title: "余额记录",
            },
            component: () => import("@/pages/coffee/finance"),
        },
        {
            path: "hotel",
            name: `${pre}hotel`,
            meta: {
                auth: ["admin-store-storeProuduct-index"],
                title: "酒店管理",
            },
            component: () => import("@/pages/coffee/hotel"),
        },
        {
            path: "sod",
            name: `${pre}sod`,
            meta: {
                auth: ["admin-store-sod-index"],
                title: "自营配送",
            },
            component: () => import("@/pages/coffee/sod"),
        },
    ],
};
