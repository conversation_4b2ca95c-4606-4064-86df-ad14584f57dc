<template>
    <div>
      <div class="i-layout-page-header">
        <PageHeader title="余额记录" hidden-breadcrumb></PageHeader>
      </div>
      <Card :bordered="false" dis-hover class="ivu-mt">
        <Form
          ref="formValidate"
          :model="formValidate"
          :label-width="labelWidth"
          :label-position="labelPosition"
          class="tabform"
          @submit.native.prevent
        >
          <Row :gutter="24" type="flex">
            <Col :xl="6" :lg="6" :md="12" :sm="24" :xs="24">
              <FormItem label="手机号：">
                <Input
                  enter-button
                  placeholder="请输入"
                  element-id="name"
                  v-model="formValidate.phone"
                />
              </FormItem>
            </Col>
            <Col :xl="6" :lg="6" :md="12" :sm="24" :xs="24">
              <FormItem label="时间范围：" class="tab_data">
                <DatePicker
                  :editable="false"
                  style="width: 80%"
                  @on-change="onchangeTime"
                  format="yyyy/MM/dd"
                  type="daterange"
                  placement="bottom-end"
                  placeholder="自定义时间"
                ></DatePicker>
              </FormItem>
            </Col>
            <Col :xl="6" :lg="6" :md="12" :sm="24" :xs="24">
              <FormItem label="筛选类型：" class="tab_data">
                <Select
                  v-model="formValidate.type"
                  style="width: 200px"
                  clearable
                >
                  <Option
                    v-for="(item, index) in billList"
                    :key="index"
                    :value="item.type"
                    >{{ item.title }}</Option
                  >
                </Select>
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem>
                <Button type="primary" icon="ios-search" @click="userSearchs"
                  >搜索</Button
                >
              </FormItem>
            </Col>
          </Row>
        </Form>
      </Card>
      <cards-data :cardLists="cardLists"></cards-data>
      <Card :bordered="false" dis-hover>
        <Table
          ref="table"
          highlight-row
          :columns="columns"
          :data="tabList"
          :loading="loading"
          no-data-text="暂无数据"
          no-filtered-data-text="暂无筛选结果"
        >
          <template slot-scope="{ row }" slot="number">
            <div :class="[row.pm === 1 ? 'green' : 'red']">
              {{ row.pm === 1 ? row.number : "-" + row.number }}
            </div>
          </template>
          <template slot-scope="{ row }" slot="order">
            {{ row.pm === 1 ? '充值订单' : "支付商品订单号：" + row.link_id }}
          </template>
        </Table>
        <div class="acea-row row-right page">
          <Page
            :total="total"
            :current="formValidate.page"
            show-elevator
            show-total
            :page-size="formValidate.limit"
            @on-change="pageChange"
          />
        </div>
      </Card>
    </div>
  </template>
  
  <script>
  import cardsData from "@/components/cards/cards";
  import { mapState } from "vuex";
  import {  billListApi  } from "@/api/coffee";
  export default {
    name: "coffee_bill",
    components: { cardsData },
    data() {
      return {
        cardLists: [],
        billList: [
          {
            'title':'消费',
            'type':0,
          },
          {
            'title':'充值',
            'type':1,
          },
        ],
        formValidate: {
          phone: "",
          start_time: "",
          end_time: "",
          type: "",
          page: 1, // 当前页
          limit: 10, // 每页显示条数
        },
        loading: false,
        tabList: [],
        total: 0,
        columns: [
          {
            title: "手机号",
            key: "phone",
            width: 120,
          },
          {
            title: "昵称",
            key: "nickname",
            minWidth: 50,
          },
          {
            title: "金额",
            minWidth: 50,
            slot: "number",
          },
          {
            title: "关联订单号",
            minWidth: 120,
            slot: "order",
          },
          {
            title: "类型",
            key: "title",
            minWidth: 100,
          },
          {
            title: "备注",
            key: "mark",
            minWidth: 150,
          },
          {
            title: "创建时间",
            key: "create_time",
            sortable: true,
            minWidth: 200,
          },
        ],
      };
    },
    computed: {
      ...mapState("admin/layout", ["isMobile"]),
      labelWidth() {
        return this.isMobile ? undefined : 80;
      },
      labelPosition() {
        return this.isMobile ? "top" : "right";
      },
    },
    created() {
      this.getList();
    },
    methods: {
      // 时间
      onchangeTime(e) {
        this.formValidate.start_time = e[0];
        this.formValidate.end_time = e[1];
      },
      // 列表
      getList() {
        this.loading = true;
        billListApi(this.formValidate)
          .then(async (res) => {
            let data = res.data;
            this.tabList = data.data;
            this.total = data.count;
            this.loading = false;
            this.cardLists = [
                {
                  col: 6,
                  count: data.stat.pm_2,
                  name: "充值金额",
                  className: "md-basket",
                },
                {
                  col: 6,
                  count: data.stat.pm_1,
                  name: "消费金额",
                  className: "md-pricetags",
                }
            ]
          })
          .catch((res) => {
            this.loading = false;
            this.$Message.error(res.msg);
          });
      },
      pageChange(index) {
        this.formValidate.page = index;
        this.getList();
      },
      // 搜索
      userSearchs() {
        this.formValidate.page = 1;
        this.getList();
      }
    },
  };
  </script>
  
  <style scoped lang="stylus">
  .ivu-form-label-left >>> .ivu-form-item-label {
    text-align: right;
  }
  
  .tabform .export {
    margin-left: 10px;
  }
  
  .red {
    color: #FF5722;
  }
  
  .green {
    color: #009688;
  }
  </style>
  