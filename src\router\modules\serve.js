import BasicLayout from "@/layouts/basic-layout";

const pre = "serve_"; // 前缀

export default {
    path: "/admin/serve",
    name: "serve", // 当前路由的name
    header: "serve",
    meta: {

    },
    redirect: {
        name: `${pre}haircut`,
    },
    component: BasicLayout,
    children: [
        {
            path: "haircut", // redirect path
            name: `${pre}haircut`,
            meta: {
                title: "理发服务",
            },
            component: () => import("@/pages/serve/haircut"),
        },
        {
            path: 'setting/:type?/:tab_id?', // redirect path
            name: `${pre}setting`,
            meta: {
                title: "理发设置",
            },
            component: () => import("@/pages/serve/setting"),
        },
        {
            path: 'wechat/:type?/:tab_id?', // redirect path
            name: `${pre}setting`,
            meta: {
                title: "小程序配置",
            },
            component: () => import("@/pages/serve/setting/wechat"),
        },
        {
            path: 'menu', // redirect path
            name: `${pre}setting`,
            meta: {
                footer: true,
                title: "商品库管理",
            },
            component: () => import("@/pages/coffee/menu"),
        },
        {
            path: "add_product/:id?",
            name: `${pre}productAdd`,
            meta: {
                title: "商品库添加",
            },
            component: () => import("@/pages/coffee/menu/addForm"),
        },
    ],
};
