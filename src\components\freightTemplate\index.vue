<template>
    <div>
        <Modal v-model="isTemplate" title="运费模版" width="70%" if="isTemplate" @on-cancel="cancel">
            <div class="Modals">
                <Form
                    class="form"
                    ref="formData"
                    :label-width="labelWidth"
                    :label-position="labelPosition"
                >
                    <Row :gutter="24" type="flex">
                        <Col :xl="18" :lg="18" :md="18" :sm="24" :xs="24">
                            <FormItem label="模板名称：" prop="name">
                                <Input
                                    type="text"
                                    placeholder="请输入模板名称"
                                    :maxlength="20"
                                    v-model="formData.name"
                                />
                            </FormItem>
                        </Col>
                    </Row>
                    <Row :gutter="24" type="flex">
                        <Col :xl="18" :lg="18" :md="18" :sm="24" :xs="24">
                            <FormItem label="计费方式：" props="state" label-for="state">
                                <RadioGroup
                                    class="radio"
                                    v-model="formData.type"
                                    @on-change="changeRadio"
                                    element-id="state"
                                >
                                    <Radio :label="1">按件数</Radio>
                                    <Radio :label="2">按重量</Radio>
                                    <Radio :label="3">按体积</Radio>
                                </RadioGroup>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row :gutter="24" type="flex">
                        <Col :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
                            <FormItem
                                class="label"
                                label="配送区域及运费："
                                props="state"
                                label-for="state"
                            >
                                <Table
                                    ref="table"
                                    :columns="columns"
                                    :data="templateList"
                                    class="ivu-mt"
                                    no-data-text="暂无数据"
                                    border
                                >
                                    <template slot-scope="{ index }" slot="regionName">
                                        <div v-if="index === 0">
                                            默认全国
                                            <span style="font-weight: bold">(开启指定区域不配送时无效)</span>
                                        </div>
                                        <LazyCascader
                                            v-else
                                            v-model="templateList[index].city_ids"
                                            style="width: 98%"
                                            :props="props"
                                            collapse-tags
                                            clearable
                                            :filterable="false"
                                            size="mini"
                                        />
                                        <!--<el-cascader ref="cascader" v-model="templateList[index].city_ids" :props="propsData" clearable v-else size="mini"></el-cascader>-->
                                    </template>
                                    <template slot-scope="{ row, index }" slot="action">
                                        <a
                                            v-if="index === 0"
                                            @click="delCity(row, '配送区域', index, 1)"
                                        >删除</a>
                                    </template>
                                </Table>
                                <Row type="flex" class="addTop">
                                    <Col>
                                        <Button
                                            type="primary"
                                            icon="md-add"
                                            @click="addCity(1)"
                                        >单独添加配送区域</Button>
                                    </Col>
                                </Row>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row :gutter="24" type="flex">
                        <Col :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
                            <FormItem label="指定包邮：" prop="store_name" label-for="store_name">
                                <Radio-group class="radio" v-model="formData.appoint_check">
                                    <Radio :label="1">开启</Radio>
                                    <Radio :label="0">关闭</Radio>
                                </Radio-group>
                                <Table
                                    ref="table"
                                    :columns="columns2"
                                    :data="appointList"
                                    class="addTop ivu-mt"
                                    no-data-text="暂无数据"
                                    border
                                    v-if="formData.appoint_check === 1"
                                >
                                    <template slot-scope="{ index }" slot="placeName">
                                        <LazyCascader
                                            v-model="appointList[index].city_ids"
                                            style="width: 98%"
                                            :props="props"
                                            collapse-tags
                                            clearable
                                            :filterable="false"
                                            size="mini"
                                        />
                                        <!--<el-cascader v-model="appointList[index].city_ids" :props="propsData" clearable size="mini" @expand-change="onChange"></el-cascader>-->
                                    </template>
                                    <template slot-scope="{ row, index }" slot="action">
                                        <a @click="delCity(row, '配送区域', index, 2)">删除</a>
                                    </template>
                                </Table>
                                <Row type="flex" class="addTop" v-if="formData.appoint_check === 1">
                                    <Col>
                                        <Button
                                            type="primary"
                                            icon="md-add"
                                            @click="addCity(2)"
                                        >单独指定包邮</Button>
                                    </Col>
                                </Row>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row :gutter="24" type="flex">
                        <Col :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
                            <FormItem label="指定不送达：" prop="store_name" label-for="store_name">
                                <Radio-group class="radio" v-model="formData.no_delivery_check">
                                    <Radio :label="1">开启</Radio>
                                    <Radio :label="0">关闭</Radio>
                                </Radio-group>
                                <Table
                                    ref="table"
                                    :columns="columns3"
                                    :data="noDeliveryList"
                                    class="addTop ivu-mt"
                                    no-data-text="暂无数据"
                                    border
                                    v-if="formData.no_delivery_check === 1"
                                >
                                    <template slot-scope="{ index }" slot="placeName">
                                        <LazyCascader
                                            v-model="noDeliveryList[index].city_ids"
                                            style="width: 98%"
                                            :props="props"
                                            collapse-tags
                                            clearable
                                            :filterable="false"
                                            size="mini"
                                        />
                                        <!--<el-cascader v-model="noDeliveryList[index].city_ids" :props="propsData" clearable size="mini"></el-cascader>-->
                                    </template>
                                    <template slot-scope="{ row, index }" slot="action">
                                        <a @click="delCity(row, '配送区域', index, 3)">删除</a>
                                    </template>
                                </Table>
                                <Row
                                    type="flex"
                                    class="addTop"
                                    v-if="formData.no_delivery_check === 1"
                                >
                                    <Col>
                                        <Button
                                            type="primary"
                                            icon="md-add"
                                            @click="addCity(3)"
                                        >单独指定不送达</Button>
                                    </Col>
                                </Row>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row :gutter="24" type="flex">
                        <Col :xl="18" :lg="18" :md="18" :sm="24" :xs="24">
                            <FormItem label="排序：" prop="store_name" label-for="store_name">
                                <InputNumber
                                    :min="0"
                                    placeholder="输入值越大越靠前"
                                    v-model="formData.sort"
                                ></InputNumber>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row :gutter="24" type="flex">
                        <Col>
                            <FormItem prop="store_name" label-for="store_name">
                                <Button type="primary" @click="handleSubmit">
                                    {{
                                        id ? "立即修改" : "立即提交"
                                    }}
                                </Button>
                            </FormItem>
                        </Col>
                    </Row>
                </Form>
            </div>
            <div slot="footer"></div>
        </Modal>
    </div>
</template>

<script>
    import "element-ui/lib/theme-chalk/index.css";
    import { mapState } from "vuex";
    import city from "@/components/freightTemplate/city";
    import { templatesSaveApi, shipTemplatesApi, cityData } from "@/api/setting";
    import LazyCascader from "../lazyCascader";
    const cacheAddress = {};
    export default {
        name: "freightTemplate",
        components: { LazyCascader },
        props: {},
        data() {
            let that = this;
            return {
                props: {
                    children: "children",
                    label: "label",
                    value: "value",
                    multiple: true,
                    lazy: true,
                    lazyLoad: this.lazyLoad,
                    checkStrictly: true,
                },
                // propsData:{
                //     multiple: true,
                //     checkStrictly: true,
                //     lazy: true,
                //     lazyLoad (node, resolve) {
                //         cityData({pid:node?node.value:0}).then(res=>{
                //             let nodes = res.data
                //             nodes.forEach(item => {
                //                 item.leaf = !item.hasOwnProperty('children')
                //             })
                //             console.log('pppp',nodes);
                //             resolve(nodes)
                //         }).catch(err=>{
                //             this.$Message.error(err.msg)
                //         })
                //     }
                // },
                isTemplate: false,
                columns: [
                    {
                        title: "可配送区域",
                        slot: "regionName",
                        minWidth: 200,
                    },
                    {
                        title: "首件",
                        key: "first",
                        minWidth: 70,
                        render: (h, params) => {
                            return h("Input", {
                                props: {
                                    type: "number",
                                    // size: 'small',
                                    value: that.templateList[params.index].first, // 此处如何让数据双向绑定
                                },
                                on: {
                                    "on-change": (event) => {
                                        that.templateList[params.index].first = event.target.value;
                                    },
                                },
                            });
                        },
                    },
                    {
                        title: "运费（元）",
                        key: "price",
                        minWidth: 70,
                        render: (h, params) => {
                            return h("Input", {
                                props: {
                                    type: "number",
                                    // size: 'small',
                                    value: that.templateList[params.index].first_price, // 此处如何让数据双向绑定
                                },
                                on: {
                                    "on-change": (event) => {
                                        that.templateList[params.index].first_price =
                                            event.target.value;
                                    },
                                },
                            });
                        },
                    },
                    {
                        title: "续件",
                        key: "continue",
                        minWidth: 70,
                        render: (h, params) => {
                            return h("Input", {
                                props: {
                                    type: "number",
                                    // size: 'small',
                                    value: that.templateList[params.index].continue, // 此处如何让数据双向绑定
                                },
                                on: {
                                    "on-change": (event) => {
                                        that.templateList[params.index].continue = event.target.value;
                                    },
                                },
                            });
                        },
                    },
                    {
                        title: "续费（元）",
                        key: "continue_price",
                        minWidth: 70,
                        render: (h, params) => {
                            return h("Input", {
                                props: {
                                    type: "number",
                                    // size: 'small',
                                    value: that.templateList[params.index].continue_price, // 此处如何让数据双向绑定
                                },
                                on: {
                                    "on-change": (event) => {
                                        that.templateList[params.index].continue_price =
                                            event.target.value;
                                    },
                                },
                            });
                        },
                    },
                    {
                        title: "操作",
                        slot: "action",
                        minWidth: 70,
                    },
                ],
                columns2: [
                    {
                        title: "选择地区",
                        slot: "placeName",
                        minWidth: 250,
                    },
                    {
                        title: "包邮件数",
                        key: "number",
                        minWidth: 100,
                        render: (h, params) => {
                            return h("Input", {
                                props: {
                                    type: "number",
                                    // size: 'small',
                                    value: that.appointList[params.index].number, // 此处如何让数据双向绑定
                                },
                                on: {
                                    "on-change": (event) => {
                                        that.appointList[params.index].number = event.target.value;
                                    },
                                },
                            });
                        },
                    },
                    {
                        title: "包邮金额（元）",
                        key: "price",
                        minWidth: 100,
                        render: (h, params) => {
                            return h("Input", {
                                props: {
                                    type: "number",
                                    // size: 'small',
                                    value: that.appointList[params.index].price, // 此处如何让数据双向绑定
                                },
                                on: {
                                    "on-change": (event) => {
                                        that.appointList[params.index].price = event.target.value;
                                    },
                                },
                            });
                        },
                    },
                    {
                        title: "操作",
                        slot: "action",
                        minWidth: 100,
                    },
                ],
                columns3: [
                    {
                        title: "选择地区",
                        slot: "placeName",
                        minWidth: 250,
                    },
                    {
                        title: "操作",
                        slot: "action",
                        minWidth: 100,
                    },
                ],
                templateList: [
                    {
                        first: 1,
                        first_price: 0,
                        continue: 1,
                        continue_price: 0,
                        city_ids: [[0]],
                    },
                ],
                appointList: [],
                noDeliveryList: [],
                formData: {
                    type: 1,
                    sort: 0,
                    name: "",
                    appoint_check: 0,
                    no_delivery_check: 0,
                },
                id: 0,
            };
        },
        computed: {
            ...mapState("admin/layout", ["isMobile"]),
            labelWidth() {
                return this.isMobile ? undefined : 120;
            },
            labelPosition() {
                return this.isMobile ? "top" : "right";
            },
        },
        mounted() { },
        methods: {
            lazyLoad(node, resolve) {
                console.log("ddd", cacheAddress);
                if (cacheAddress[node]) {
                    cacheAddress[node]().then((res) => {
                        resolve([...res.data]);
                    });
                } else {
                    const p = cityData({ pid: node });
                    cacheAddress[node] = () => p;
                    p.then((res) => {
                        res.data.forEach((item) => {
                            item.leaf = !item.hasOwnProperty("children");
                        });
                        cacheAddress[node] = () =>
                            new Promise((resolve1) => {
                                setTimeout(() => resolve1(res), 300);
                        });
                        resolve(res.data);
                    }).catch((res) => {
                        this.$message.error(res.message);
                    });
                }
            },
            onChange(e) {
                // this.appointList
                console.log("hhhh", e);
            },
            editFrom(id) {
                this.id = id;

                shipTemplatesApi(id).then((res) => {
                    let formData = res.data.formData;
                    this.templateList = res.data.templateList;
                    this.appointList = res.data.appointList;
                    this.noDeliveryList = res.data.noDeliveryList;
                    this.formData = {
                        type: formData.type,
                        sort: formData.sort,
                        name: formData.name,
                        appoint_check: formData.appoint_check,
                        no_delivery_check: formData.no_delivery_check,
                    };
                    this.headerType();
                });
            },
            selectCity: function (type) {
                switch (type) {
                case 1:
                    this.templateList.push({
                        first: 1,
                        first_price: 0,
                        continue: 1,
                        continue_price: 0,
                        city_ids: [],
                    });
                    break;
                case 2:
                    this.appointList.push({
                        placeName: "",
                        number: 0,
                        price: 0,
                        city_ids: [],
                    });
                    break;
                case 3:
                    this.noDeliveryList.push({
                        placeName: "",
                        city_ids: [],
                    });
                    break;
                }
            },
            // 单独添加配送区域
            addCity(type) {
                this.type = type;
                this.selectCity(type);
            },
            changeRadio() {
                this.headerType();
            },
            headerType() {
                let that = this;
                if (this.formData.type === 2) {
                    that.columns[1].title = "首重量(KG)";
                    that.columns[3].title = "续重量(KG)";
                    that.columns2[1].title = "包邮重量(KG)";
                } else if (this.formData.type === 3) {
                    that.columns[1].title = "首体积(m³)";
                    that.columns[3].title = "续体积(m³)";
                    that.columns2[1].title = "包邮体积(m³)";
                } else {
                    that.columns[1].title = "首件";
                    that.columns[3].title = "续件";
                    that.columns2[1].title = "包邮件数";
                }
            },
            // 提交
            handleSubmit: function () {
                let that = this;
                if (!that.formData.name.trim().length) {
                    return that.$Message.error("请填写模板名称");
                }
                for (let i = 0; i < that.templateList.length; i++) {
                    if (that.templateList[i].first <= 0) {
                        return that.$Message.error("首件/重量/体积应大于0");
                    }
                    if (that.templateList[i].first_price < 0) {
                        return that.$Message.error("运费应大于等于0");
                    }
                    if (that.templateList[i].continue <= 0) {
                        return that.$Message.error("续件/重量/体积应大于0");
                    }
                    if (that.templateList[i].continue_price < 0) {
                        return that.$Message.error("续费应大于等于0");
                    }
                }
                if (that.formData.appoint_check === 1) {
                    for (let i = 0; i < that.appointList.length; i++) {
                        if (that.appointList[i].number <= 0) {
                            return that.$Message.error("包邮件数应大于0");
                        }
                        if (that.appointList[i].price < 0) {
                            return that.$Message.error("包邮金额应大于等于0");
                        }
                    }
                }
                let data = {
                    no_delivery_info: that.noDeliveryList,
                    appoint_info: that.appointList,
                    region_info: that.templateList,
                    sort: that.formData.sort,
                    type: that.formData.type,
                    name: that.formData.name,
                    appoint: that.formData.appoint_check,
                    no_delivery: that.formData.no_delivery_check,
                };
                templatesSaveApi(that.id, data).then((res) => {
                    this.isTemplate = false;
                    this.$parent.getList();
                    this.formData = {
                        type: 1,
                        sort: 0,
                        name: "",
                        appoint_check: 0,
                        no_delivery_check: 0,
                    };
                    this.appointList = [];
                    this.noDeliveryList = [];
                    this.templateList = [
                        {
                            first: 1,
                            first_price: 0,
                            continue: 1,
                            continue_price: 0,
                            city_ids: [[0]],
                        },
                    ];
                    this.$Message.success(res.msg);
                });
            },
            // 删除
            delCity(row, tit, num, type) {
                let delfromData = {
                    title: tit,
                    num: num,
                    url: `setting/shipping_templates/del/${row.id}`,
                    method: "DELETE",
                    ids: "",
                };
                this.$modalSure(delfromData)
                    .then((res) => {
                        this.$Message.success(res.msg);
                        if (type === 1) {
                            this.templateList.splice(num, 1);
                        } else if (type == 2) {
                            this.appointList.splice(num, 1);
                        } else {
                            this.noDeliveryList.splice(num, 1);
                        }
                    })
                    .catch((res) => {
                        this.$Message.error(res.msg);
                });
            },
            // 关闭
            cancel() {
                this.formData = {
                    type: 1,
                    sort: 0,
                    name: "",
                    appoint_check: 0,
                    no_delivery_check: 0,
                };
                this.noDeliveryList = [];
                this.appointList = [];
                this.templateList = [
                    {
                        first: 0,
                        first_price: 0,
                        continue: 0,
                        continue_price: 0,
                        city_ids: [[0]],
                    },
                ];
            },
        },
    };
</script>
<style lang="stylus">
.el-cascader__dropdown {
  padding: 12px;
}

.el-cascader-panel {
  border: solid 1px #E4E7ED;
}

.ivu-table-border th, .ivu-table-border td {
  padding: 0 10px !important;
}

.addTop {
  margin-top: 15px;
}

.radio {
  padding: 5px 0;
}

.ivu-input-number {
  width: 100%;
}
</style>
