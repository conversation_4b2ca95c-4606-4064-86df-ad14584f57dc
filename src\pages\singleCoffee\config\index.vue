<template>
  <div>
      <div class="i-layout-page-header">
        <PageHeader class="product_tabs" title="店铺设置" hidden-breadcrumb>
        </PageHeader>
    </div>
      <div class="article-manager">
        <Card :bordered="false" dis-hover class="ivu-mt">
          <Form
            ref="formItem"
            :model="formItem"
            :label-width="labelWidth"
            :label-position="labelPosition"
            :rules="ruleValidate"
            @submit.native.prevent
          >
            <Row type="flex" :gutter="24">
              <Col span="24">
                <FormItem label="小程序主题色">
                   <div  class="acea-row">
                     <ColorPicker v-model="formItem.theme" />
                   </div>
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="首页点单图">
                   <div  class="acea-row">
                    <div class="picBox" @click="modalPicTap('place_img')">
                      <div class="pictrue" v-if="formItem.place_img"><img v-lazy="formItem.place_img"></div>
                      <div class="upLoad" v-else>
                          <div class="iconfont">+</div>
                      </div>
                  </div>
                   </div>
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="首页商城图">
                   <div  class="acea-row">
                    <div class="picBox" @click="modalPicTap('shop_img')">
                      <div class="pictrue" v-if="formItem.shop_img"><img v-lazy="formItem.shop_img"></div>
                      <div class="upLoad" v-else>
                         <div class="iconfont">+</div>
                      </div>
                  </div>
                   </div>
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="关于我们">
                   <div  class="acea-row">
                    <div class="picBox" @click="modalPicTap('about')">
                      <div class="pictrue" v-if="formItem.about"><img v-lazy="formItem.about"></div>
                      <div class="upLoad" v-else>
                          <div class="iconfont">+</div>
                      </div>
                  </div>
                   </div>
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="首页轮播图：">
                  <div class="acea-row">
                    <div
                      class="pictrue"
                      v-for="(item, index) in formItem.imgs"
                      :key="index"
                      draggable="true"
                      @dragstart="handleDragStart($event, item)"
                      @dragover.prevent="handleDragOver($event, item)"
                      @dragenter="handleDragEnter($event, item, 'imgs')"
                      @dragend="handleDragEnd($event, item)"
                    >
                      <img v-lazy="item" />
                      <Button
                        shape="circle"
                        icon="md-close"
                        @click.native="handleRemove(index, 'imgs')"
                        class="btndel"
                      ></Button>
                    </div>
                    <div
                      v-if="formItem.imgs.length < 10"
                      class="upLoad acea-row row-center-wrapper"
                      @click="modalPicTap('imgs')"
                    >
                      <Icon type="ios-camera-outline" size="26" />
                    </div>
                    <Input v-model="formItem.imgs[0]" style="display: none"></Input>
                  </div>
                  <div class="tips">
                    (最多10张
                    <br />750*750)
                  </div>
                </FormItem>
              </Col>
              <Col span="24">
                <FormItem label="菜单页轮播图：" prop="img">
                  <div class="acea-row">
                    <div
                      class="pictrue"
                      v-for="(item, index) in formItem.img"
                      :key="index"
                      draggable="true"
                      @dragstart="handleDragStart($event, item)"
                      @dragover.prevent="handleDragOver($event, item)"
                      @dragenter="handleDragEnter($event, item, 'img')"
                      @dragend="handleDragEnd($event, item)"
                    >
                      <img v-lazy="item" />
                      <Button
                        shape="circle"
                        icon="md-close"
                        @click.native="handleRemove(index, 'img')"
                        class="btndel"
                      ></Button>
                    </div>
                    <div
                      v-if="formItem.img.length < 10"
                      class="upLoad acea-row row-center-wrapper"
                      @click="modalPicTap('img')"
                    >
                      <Icon type="ios-camera-outline" size="26" />
                    </div>
                    <Input v-model="formItem.img[0]" style="display: none"></Input>
                  </div>
                  <div class="tips">
                    (最多10张
                    <br />750*750)
                  </div>
                </FormItem>
              </Col>
              <Col span="24">
                <Col v-bind="grid">
                  <FormItem label="门店名称：" prop="name" label-for="name">
                    <Input
                      v-model="formItem.name"
                      maxlength="20"
                      show-word-limit
                      style="width: 300px"
                      placeholder="请输入门店名称"
                    />
                  </FormItem>
                </Col>
              </Col>
              <Col span="24">
                <Col v-bind="grid">
                  <FormItem label="门店手机号：" label-for="phone" prop="phone">
                    <Input v-model="formItem.phone" placeholder="请输入门店手机号" style="width: 300px" />
                  </FormItem>
                </Col>
              </Col>
              <Col span="24">
                <Col v-bind="grid">
                  <FormItem label="营业时间：" label-for="day_time" prop="day_time">
                    <TimePicker
                      type="timerange"
                      @on-change="onchangeTime"
                      v-model="formItem.day_time"
                      format="HH:mm:ss"
                      :value="formItem.day_time"
                      placement="bottom-end"
                      placeholder="请选择营业时间"
                      class="inputW"
                    ></TimePicker>
                  </FormItem>
                </Col>
              </Col>
              <Col span="24">
                <Col v-bind="grid">
                  <FormItem label="营业状态：" label-for="is_status" prop="is_status">
                    <Switch
                      size="large"
                      v-model="formItem.is_status"
                      :false-value="1"
                      :true-value="0"
                    >
                      <span slot="open" :true-value="0">开启</span>
                      <span slot="close" :false-value="1">关闭</span>
                    </Switch>
                  </FormItem>
                </Col>
              </Col>
              <Col span="24">
                <Col v-bind="grid">
                  <FormItem label="门店详细地址：" label-for="address" prop="address">
                    <Input
                      search
                      enter-button="查找位置"
                      v-model="formItem.address"
                      placeholder="请输入详细地址"
                      class="inputW"
                      @on-search="onSearch"
                    />
                    <!-- 提示：定位地址后，手动补充完详细地址，禁止再次点击查找 -->
                  </FormItem>
                </Col>
              </Col>
              <Col span="24">
                <Maps
                  ref="mapChild"
                  class="map-sty"
                  :mapKey="mapKey"
                  :lat="Number(formItem.latitude || 39.988457)"
                  :lon="Number(formItem.longitude || 116.321287)"
                  :address="formItem.address"
                  @getCoordinates="getCoordinates"
                />
              </Col>
            </Row>
            <Row style="justify-content: space-around;">
              <Col>
                <Button
                  type="primary"
                  class="btn"
                  @click="handleSubmit('formItem')"
                >保存</Button>
              </Col>
            </Row>
            <Spin size="large" fix v-if="spinShow"></Spin>
          </Form>
        </Card>

        <Modal
          v-model="modalPic"
          width="950px"
          scrollable
          footer-hide
          closable
          title="上传图片"
          :mask-closable="false"
          :z-index="1"
        >
          <uploadPictures
            :isChoice="isChoice"
            @getPic="getPic"
            @getPicD="getPicD"
            :gridBtn="gridBtn"
            :gridPic="gridPic"
            v-if="modalPic"
          ></uploadPictures>
        </Modal>
      </div>
  </div>
</template>

<script>
import { singleShopUpdate, singleShopInfo} from '@/api/coffee';
import { mapState } from 'vuex';
import uploadPictures from '@/components/uploadPictures';
import Maps from '@/components/map/map.vue'
export default {
  name: 'systemStore',
  components: { uploadPictures, Maps },
  props: {},
  data() {
    let validatePhone = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写手机号'));
      } else if (!/^1[3456789]\d{9}$/.test(value)) {
        callback(new Error('手机号格式不正确!'));
      } else {
        callback();
      }
    };
    return {
      // 新增
      images: [],
      picTit: '',
      // 结束
      // 表单数据
      formItem: {
        id: 1671007433,
        // 主题
        theme: '',
        // 点单图片
        place_img:'',
        // 商城图片
        shop_img:'',
        // 关于我们
        about:'',
        // 首页轮播图
        img: [],
        // 菜单页轮播图
        imgs: [],
        // 店铺名称
        name: '',
        // 店铺手机号
        phone: '',
        // 店铺状态
        is_status: 1,
        // 店铺营业时间
        day_time: [],
        // 店铺地址
        address: '',
        latitude: '',
        longitude: '',

      },
      // 数据加载loading
      spinShow: true,
      // 验证规则
      ruleValidate: {
        name: [
          { required: true, message: '请输入门店名称', trigger: 'blur' }
        ],
        day_time: [
          { required: true, type: "array", message: "请选择营业时间", trigger: "change" },
          {
            validator(rule, value, callback, source, options) {
              if (value[0] === "") {
                callback("时间不能为空");
              }
              callback();//这个一定要有。不然无法验证通过
            }
          }
        ],//TimePicker-timerange，自定义的
        phone: [
          { required: true, validator: validatePhone, trigger: 'blur' }
        ],
        address: [
          { required: true, message: '请输入详细地址', trigger: 'blur' }
        ]
      },
      // 腾讯地图key
      mapKey: 'JNXBZ-ZNKWP-2BIDA-LRBYG-D5SB5-J7FCJ',
      // 弹窗样式
      grid: {
        xl: 20,
        lg: 20,
        md: 20,
        sm: 24,
        xs: 24
      },
      gridPic: {
        xl: 6,
        lg: 8,
        md: 12,
        sm: 12,
        xs: 12
      },
      gridBtn: {
        xl: 4,
        lg: 8,
        md: 8,
        sm: 8,
        xs: 8
      },
      // 图片上传标识
      modalPic: false,
      // 图片上传标识 多选组件
      isChoice: '单选',
      // 地图是否拾取
      isApi: 0,
    }
  },
  computed: {
    ...mapState('admin/layout', [
      'isMobile'
    ]),
    labelWidth() {
      return this.isMobile ? undefined : 120;
    },
    labelPosition() {
      return this.isMobile ? 'top' : 'right';
    }
  },
  created() {
    this.formItem.id = this.$store.state.admin.user.info.shop_id
    this.init()
  },
  methods: {
    init() {
      singleShopInfo(this.formItem).then(res => {
        this.spinShow = false;
        this.formItem = res.data
      }).catch(function (res) {
        this.spinShow = false;
        this.$Message.error(res.msg);
      })
    },
    // 地图信息获取
    getCoordinates(data) {
        this.formItem.latitude = data.location.lat || 39.988457
        this.formItem.longitude = data.location.lng || 116.321287
        let components = data.addressComponents;
        if (this.formItem.address.indexOf(components.street) == -1) {
          this.formItem.address = data.address + (components.town ? components.town : '');
        }
    },
    // 查找位置
    onSearch() {
      if (this.$refs.mapChild) {
        this.$refs.mapChild.searchKeyword(this.formItem.address)
      }
    },
    // 选择图片
    modalPicTap(picTit) {
      this.modalPic = true;
      // 轮播图
      this.picTit = picTit;
      switch (picTit) {
        case 'image':
          this.isChoice = "单选"
          break;
        case 'place_img':
          this.isChoice = "单选"
          break;
        case 'shop_img':
          this.isChoice = "单选"
          break;
        case 'about':
          this.isChoice = "单选"
          break;
        default:
          this.isChoice = "多选"
          break;
      }
    },
    // 选中图片
    getPic(pc) {
      switch (this.picTit) {
        case "img":
          this.formItem.img = pc.att_dir;
          break;
        case "imgs":
          this.formItem.imgs = pc.att_dir;
          break;
        case "place_img":
          this.formItem.place_img = pc.att_dir;
          break;
        case "shop_img":
          this.formItem.shop_img = pc.att_dir;
          break;
        case "about":
          this.formItem.about = pc.att_dir;
          break;
        default:
          this.formItem.image = pc.att_dir;
      }
      this.modalPic = false;
    },
    // 营业时间
    onchangeTime(e) {
      this.formItem.day_time = e;
    },
    // 提交
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.formItem.day_time[0] == '') {
            this.formItem.day_time = ['00:00:00', '23:59:59']
          }
          // this.formItem.valid_range = this.formItem.valid_range/1000 
          this.formItem.id = this.$store.state.admin.user.info.shop_id
          let params = this.formItem
          console.log(params)
          singleShopUpdate(params).then(async res => {
            this.$Message.success(res.msg);
          }).catch(res => {
            this.$Message.error(res.msg);
          })
        } else {
          return false;
        }
      })

    },
    // 轮播图方法获取多张图信息
    getPicD(pc) {
      this.images = pc;
      this.images.map((item) => {
        switch (this.picTit) {
          case "img":
            this.formItem.img.push(item.att_dir);
            this.formItem.img = this.formItem.img.splice(
              0,
              10
            );
            break;
          default:
            this.formItem.imgs.push(item.att_dir);
            this.formItem.imgs = this.formItem.imgs.splice(
              0,
              10
            );
        }

      });
      this.modalPic = false;
    },
    handleDragStart(e, item) {
      this.dragging = item;
    },
    handleDragEnd(e, item) {
      this.dragging = null;
    },
    handleDragOver(e) {
      e.dataTransfer.dropEffect = "move";
    },
    handleDragEnter(e, item, picTit) {
      e.dataTransfer.effectAllowed = "move";
      if (item === this.dragging) {
        return;
      }
      if (picTit == 'img') {
        const newItems = [...this.formItem.img];
        const src = newItems.indexOf(this.dragging);
        const dst = newItems.indexOf(item);
        newItems.splice(dst, 0, ...newItems.splice(src, 1));
        this.formItem.img = newItems;
      } else {
        const newItems = [...this.formItem.imgs];
        const src = newItems.indexOf(this.dragging);
        const dst = newItems.indexOf(item);
        newItems.splice(dst, 0, ...newItems.splice(src, 1));
        this.formItem.imgs = newItems;
      }
    },
    handleRemove(i, picTit) {
      switch (picTit) {
        case "img":
          this.images.splice(i, 1);
          this.formItem.img.splice(i, 1);
          break;
        case "imgs":
          this.images.splice(i, 1);
          this.formItem.imgs.splice(i, 1);
          break;
        default:
          this.images.splice(i, 1);
          this.formItem.image.splice(i, 1);
      }
    }
  }
}
</script>

<style scoped lang="stylus">
  .pictrue {
    width: 60px;
    height: 60px;
    border: 1px dotted rgba(0, 0, 0, 0.1);
    margin-right: 15px;
    margin-bottom 10px;
    display: inline-block;
    position: relative;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }

    .btndel {
      position: absolute;
      z-index: 1;
      width: 20px !important;
      height: 20px !important;
      left: 46px;
      top: -4px;
    }
  }
  .upLoad {
    width: 58px;
    height: 58px;
    line-height: 58px;
    border: 1px dotted rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.02);
    cursor: pointer;
  }
	.map-sty {
		width: 90%;
		text-align: right;
		margin: 0 0 0 10%;
	}
	/deep/.ivu-card-body{
		padding 16px 0 16px 0!important;
	}
	.footer{
		width 100%;
		height 50px;
		box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.05);
		margin-top 50px;
	}
	/deep/.ivu-btn-primary{
		width 86px;
	}
	.btn{
		margin-top: 20px;
	}
	.inputW{
		width 400px;
	}
	.ivu-mt{
		min-width 580px;
	}
	.picBox
		display: inline-block;
		cursor: pointer;
		.upLoad
			width: 58px;
			height: 58px;
			line-height: 58px;
			border: 1px dotted rgba(0, 0, 0, 0.1);
			border-radius: 4px;
			background: rgba(0, 0, 0, 0.02);
		.pictrue
			width: 60px;
			height: 60px;
			border: 1px dotted rgba(0, 0, 0, 0.1);
			margin-right: 10px;
			img
				width: 100%;
				height: 100%;
		.iconfont
			color: #CCCCCC;
			font-size 26px;
			text-align center
			
</style>
