<template>
  <div class="goodList">
      <Form ref="formValidate" :model="formValidate" :label-width="labelWidth" :label-position="labelPosition"
          class="tabform">
          <Row type="flex" :gutter="24">
              <Col v-bind="grid">
              <FormItem label="手机号搜索：" label-for="phone">
                  <Input search enter-button placeholder="请输入手机号" v-model="formValidate.phone" style="width: 240px"
                      @on-search="userSearchs" />
              </FormItem>
              </Col>
          </Row>
      </Form>
      <Table ref="table" no-data-text="暂无数据"  no-filtered-data-text="暂无筛选结果"
          max-height="400" :columns="columns" :data="tableList" :loading="loading">
          <template slot-scope="{ row, index }" slot="avatars">
          <viewer>
              <div class="tabBox_img">
              <img v-lazy="row.headimgurl" />
              </div>
          </viewer>
          </template>
          <template slot-scope="{ row, index }" slot="nickname">
              <div class="acea-row">
                  <Icon
                  type="md-male"
                  v-show="row.sex == 1"
                  color="#2db7f5"
                  size="15"
                  class="mr5"
                  />
                  <Icon
                  type="md-female"
                  v-show="row.sex == 2"
                  color="#ed4014"
                  size="15"
                  class="mr5"
                  />
                  <div v-text="row.nickname"></div>
              </div>
          </template>
      </Table>
      <div class="acea-row row-right page">
          <Page :total="total" show-elevator show-total @on-change="pageChange" :page-size="formValidate.limit" />
      </div>
      <div class="footer" slot="footer">
          <Button type="primary" size="large" :loading="modal_loading" long @click="ok">提交</Button>
      </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { userList } from "@/api/air";
import {
orderYueCreate,
} from '@/api/coffee';
export default {
  name: "index",
  props: {
  pay_price: {
    type: Number,
    value: 0
  },
  onchange_price: {
    type: Number|Object,
    value: 0
  },
  shop_id: {
    type: String,
    default: "",
  },
},
  data() {
      return {
          modal_loading: false,
          formValidate: {
              page: 1,
              limit: 15,
              shop_id: '',
              category_id: "",
              store_name: "",
          },
          total: 0,
          loading: false,
          grid: {
              xl: 10,
              lg: 10,
              md: 12,
              sm: 24,
              xs: 24,
          },
          userId:'',
          money:'',
          tableList: [],
          currentid: 0,
          columns: [
             {
                  title: "头像",
                  slot: "avatars",
                  minWidth: 60,
                  },
                  {
                  title: "昵称",
                  slot: "nickname",
                  minWidth: 120,
                  },
                  {
                  title: "手机号",
                  key: "phone",
                  minWidth: 100,
                  },
                          {
                  title: "余额",
                  key: "money",
                  sortable: "custom",
                  minWidth: 100,
                  },
                  {
                  title: "消费次数",
                  key: "consume_count",
                  minWidth: 50,
                  },
                      {
                  title: "消费金额",
                  key: "consume_money",
                  minWidth: 50,
                  },
                  {
                  title: "创建时间",
                  key: "create_time",
                  minWidth: 150,
                  },
          ],
      };
  },
  computed: {
      ...mapState("admin/layout", ["isMobile"]),
      labelWidth() {
          return this.isMobile ? undefined : 120;
      },
      labelPosition() {
          return this.isMobile ? "top" : "right";
      },
  },
created() {
  let radio = {
    width: 60,
    align: "center",
    render: (h, params) => {
      let id = params.row.id;
      let flag = false;
      if (this.currentid === id) {
        flag = true;
      } else {
        flag = false;
      }
      let self = this;
      return h("div", [
        h("Radio", {
          props: {
            value: flag,
          },
          on: {
            "on-change": () => {
              self.currentid = id;
              this.userId = params.row.id
              this.money = Number(params.row.money);
            },
          },
        }),
      ]);
    },
  };
  this.columns.unshift(radio);
},
  mounted() {
      this.getList();
      console.log(this.onchange_price)
  },
  methods: {
      handleSelectAll() {
          this.$refs.table.selectAll(false);
      },
      pageChange(index) {
          this.formValidate.page = index;
          this.getList();
      },
      // 列表
      getList() {
          this.loading = true;
          userList(this.formValidate)
              .then(async (res) => {
                  let data = res.data;
                  this.tableList = data.list;
                  this.total = res.data.count;
                  this.loading = false;
              })
              .catch((res) => {
                  this.loading = false;
                  this.$Message.error(res.msg);
              });

      },
      ok() {
          if(this.userId) {
              if(this.money < this.pay_price) {
                  this.$Message.warning("用户余额小于" + this.pay_price + "元");
              } else {
                  this.modal_loading = true
                  orderYueCreate({shopId:this.shop_id, userId:this.userId, pay_price:this.pay_price,onchange_price:this.onchange_price}).then(res => {
                      this.$Message.success(res.msg)
                      this.modal_loading = false
                      this.$emit("closeUser", false);
                  }).catch(err => {
                      this.$Message.error(err.msg)
                      this.modal_loading = false
                  })
              }
          } else {
            this.$Message.warning("请先选择用户");
          }
      },
      // 表格搜索
      userSearchs() {
          this.formValidate.page = 1;
          this.getList();
      },
  },
};
</script>

<style scoped lang="stylus">
.footer {
margin: 15px 0;
}

.tabBox_img {
width: 36px;
height: 36px;
border-radius: 4px;
cursor: pointer;

img {
  width: 100%;
  height: 100%;
}
}

.tabform {
>>> .ivu-form-item {
  margin-bottom: 16px !important;
}
}

.btn {
margin-top: 20px;
float: right;
}

.goodList {
>>> table {
  width: 100% !important;
}
}
</style>
