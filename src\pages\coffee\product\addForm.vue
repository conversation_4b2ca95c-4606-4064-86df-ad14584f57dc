<template>
  <div class="article-manager video-icon" id="shopp-manager">
    <div class="i-layout-page-header">
      <PageHeader class="product_tabs" hidden-breadcrumb>
        <div slot="title">
          <router-link :to="{ path: '/admin/coffee/product_list' }">
            <Button icon="ios-arrow-back" size="small" class="mr20">返回</Button>
          </router-link>
          <span v-text="$route.params.id ? '编辑商品' : '添加商品'" class="mr20"></span>
        </div>
      </PageHeader>
    </div>
    <Card :bordered="false" dis-hover class="ivu-mt">
      <Tabs v-model="currentTab" @on-click="onhangeTab">
        <TabPane label="商品信息" name="1"></TabPane>
      </Tabs>
      <Form
        class="formValidate mt20"
        ref="formValidate"
        :rules="ruleValidate"
        :model="formValidate"
        :label-width="labelWidth"
        :label-position="labelPosition"
        @submit.native.prevent
      >
        <Row :gutter="24" type="flex" v-show="currentTab === '1'">
          <!-- 商品信息-->
          <Col span="24">
            <FormItem label="店铺：" prop="shop_id">
              <div class="acea-row">
                <Select
                  v-model="formValidate.shop_id"
                  v-width="'50%'"
                  class="mr20"
                  @on-change="shopChange"
                >
                  <Option
                    v-for="(item, index) in shopList"
                    :value="item.id"
                    :key="index"
                  >{{ item.name }}</Option>
                </Select>
              </div>
            </FormItem>
          </Col>
          <Col span="24">
            <FormItem label="分类：" prop="category_id">
              <div class="acea-row">
                <Select
                  v-model="formValidate.category_id"
                  :filterable="true"
                  v-width="'50%'"
                  class="mr20"
                >
                  <Option
                    v-for="(item, index) in categoryList"
                    :value="item.id"
                    :key="index"
                  >{{ item.name }}</Option>
                </Select>
              </div>
            </FormItem>
          </Col>
          <Col span="24">
            <FormItem label="商品名称：" prop="name">
              <Input v-model="formValidate.name" placeholder="请输入商品名称" v-width="'50%'" />
            </FormItem>
          </Col>
          <Col span="24">
            <FormItem label="商品封面图：" prop="img">
              <div class="pictrueBox" @click="modalPicTap('img')">
                <div class="pictrue" v-if="formValidate.img">
                  <img v-lazy="formValidate.img" />
                  <Input v-model="formValidate.img" style="display: none"></Input>
                </div>
                <div class="upLoad acea-row row-center-wrapper" v-else>
                  <Input v-model="formValidate.img" style="display: none"></Input>
                  <Icon type="ios-camera-outline" size="26" />
                </div>
              </div>
              <div class="tips">(345*345)</div>
            </FormItem>
          </Col>
          <Col>
            <FormItem label="商品详情图：" prop="img">
              <div class="pictrueBox" @click="modalPicTap('images')">
                <div class="pictrue" v-if="formValidate.images">
                  <img v-lazy="formValidate.images" />
                  <Input v-model="formValidate.images" style="display: none"></Input>
                </div>
                <div class="upLoad acea-row row-center-wrapper" v-else>
                  <Input v-model="formValidate.images" style="display: none"></Input>
                  <Icon type="ios-camera-outline" size="26" />
                </div>
              </div>
              <div class="tips">不填则使用上图</div>
            </FormItem>
          </Col>
          <Col span="24">
            <FormItem label="商品简介：" prop="info">
              <Input
                v-model="formValidate.info"
                type="textarea"
                :rows="3"
                placeholder="请输入商品简介"
                v-width="'50%'"
              />
            </FormItem>
          </Col>
          <Col v-bind="grid">
            <FormItem label="园区价：">
              <InputNumber
                v-width="'90%'"
                :min="0"
                v-model="formValidate.price"
                placeholder="请输入园区价"
              />
            </FormItem>
          </Col>
          <Col v-bind="grid">
            <FormItem label="门市价：">
              <InputNumber
                v-width="'90%'"
                :min="0"
                v-model="formValidate.retail_price"
                placeholder="请输入门市价"
              />
            </FormItem>
          </Col>
          <Col v-bind="grid">
            <FormItem label="成本价：">
              <InputNumber
                v-width="'90%'"
                :min="0"
                v-model="formValidate.cost"
                placeholder="请输入成本价"
              />
            </FormItem>
          </Col>
          <Col v-bind="grid">
            <FormItem label="排序：">
              <InputNumber
                :min="0"
                v-width="'90%'"
                v-model="formValidate.sort"
                placeholder="请输入排序"
              />
              <div class="tips">(越小越靠前)</div>
            </FormItem>
          </Col>
          <Col span="24">
            <FormItem label="选择规格：" prop>
              <div class="acea-row row-middle">
                <Select v-model="selectRule" style="width: 23%">
                  <Option
                    v-for="(item, index) in ruleList"
                    :value="item.rule_name"
                    :key="index"
                  >{{ item.rule_name }}</Option>
                </Select>
                <Button type="primary" class="mr20" @click="confirm">确认</Button>
                <Button @click="addRule">添加规格模板</Button>
              </div>
            </FormItem>
          </Col>
          <Col span="24">
            <FormItem v-if="this.formValidate.attrs.length !== 0">
              <draggable
                class="dragArea list-group"
                :list="formValidate.attrs"
                group="peoples"
                handle=".move-icon"
                :move="checkMove"
                @end="end"
              >
                <div
                  v-for="(item, index) in this.formValidate.attrs"
                  :key="index"
                  class="acea-row row-middle mb10"
                >
                  <div class="move-icon">
                    <span class="iconfont icondrag2"></span>
                  </div>
                  <div style="width: 90%" :class="moveIndex === index ? 'borderStyle' : ''">
                    <div class="acea-row row-middle">
                      <span class="mr5">{{ item.value }}</span>
                      <Icon
                        type="ios-close-circle"
                        size="14"
                        class="curs"
                        @click="handleRemoveRole(index)"
                      />
                    </div>
                    <div class="rulesBox">
                      <draggable :list="item.detail" handle=".drag">
                        <Tag
                          type="dot"
                          closable
                          color="primary"
                          v-for="(j, indexn) in item.detail"
                          :key="indexn"
                          :name="j"
                          class="mr20 drag"
                          @on-close="handleRemove2(item.detail, indexn)"
                        >{{ j }}</Tag>
                      </draggable>
                      <Input placeholder="加料名" v-model="item.detail.attrsVal" style="width: 100px" />
                      <Input
                        search
                        enter-button="添加"
                        placeholder="价格"
                        v-model="item.detail.price"
                        @on-search="createAttr(item.detail.attrsVal, index, item.detail.price)"
                        style="width: 130px"
                      />
                    </div>
                  </div>
                </div>
              </draggable>
            </FormItem>
          </Col>
          <Col span="24">
            <FormItem>
              <Button type="primary" icon="md-add" @click="addBtn" class="mr15">添加新规格</Button>
            </FormItem>
          </Col>
          <Col span="24" v-if="showIput">
            <Col :xl="6" :lg="9" :md="10" :sm="24" :xs="24">
              <FormItem label="规格：">
                <Input placeholder="请输入规格" v-model="formDynamic.attrsName" />
              </FormItem>
            </Col>
            <Col :xl="6" :lg="9" :md="10" :sm="24" :xs="24">
              <FormItem label="规格值：">
                <Input v-model="formDynamic.attrsVal" placeholder="请输入加料名" />
                <Input v-model="formDynamic.price" placeholder="请输入价格" />
              </FormItem>
            </Col>
            <Col :xl="6" :lg="5" :md="10" :sm="24" :xs="24">
              <FormItem>
                <Button type="primary" class="mr15" @click="createAttrName">确定</Button>
                <Button @click="offAttrName">取消</Button>
              </FormItem>
            </Col>
          </Col>
          <Col v-bind="grid">
            <FormItem label="上架时间：">
              <TimePicker v-model="time" format="HH:mm:ss" type="timerange"  placement="bottom-end" placeholder="上架时间" style="width: 168px"></TimePicker>
            </FormItem>
          </Col>
          <Col span="24">
            <Col v-bind="grid">
              <FormItem label="限售：">
                <RadioGroup v-model="formValidate.is_stock">
                  <Radio :label="0" class="radio">关闭</Radio>
                  <Radio :label="1">开启</Radio>
                </RadioGroup>
              </FormItem>
            </Col>
            <Col v-bind="grid">
              <FormItem label="限售量：">
                <InputNumber
                  v-width="'90%'"
                  :min="0"
                  v-model="formValidate.stock"
                  placeholder="请输入"
                />
              </FormItem>
            </Col>
          </Col>
          <Col span="24">
            <Col v-bind="grid">
              <FormItem label="酒店轻食：">
                <RadioGroup v-model="formValidate.is_hotel">
                  <Radio :label="2">是</Radio>
                  <Radio :label="1" class="radio">否</Radio>
                </RadioGroup>
              </FormItem>
            </Col>
            <Col v-bind="grid">
              <FormItem label="自营配送：">
                <RadioGroup v-model="formValidate.is_sod">
                  <Radio :label="2">开启</Radio>
                  <Radio :label="1" class="radio">关闭</Radio>
                </RadioGroup>
              </FormItem>
            </Col>
            <Col v-bind="grid">
              <FormItem label="活动价：">
                <RadioGroup v-model="formValidate.is_activity">
                  <Radio :label="2" class="radio">显示</Radio>
                  <Radio :label="1">不显示</Radio>
                </RadioGroup>
              </FormItem>
            </Col>
          </Col>
          <Col v-bind="grid">
            <FormItem label="商品状态：">
              <RadioGroup v-model="formValidate.is_status">
                <Radio :label="0" class="radio">开启</Radio>
                <Radio :label="1">关闭</Radio>
              </RadioGroup>
            </FormItem>
          </Col>
        </Row>
        <FormItem>
          <Button
            type="primary"
            :disabled="openSubimit"
            class="submission"
            @click="handleSubmit('formValidate')"
          >保存</Button>
        </FormItem>
        <Spin size="large" fix v-if="spinShow"></Spin>
      </Form>
      <Modal
        v-model="modalPic"
        width="950px"
        scrollable
        footer-hide
        closable
        title="上传商品图"
        :mask-closable="false"
        :z-index="1"
      >
        <uploadPictures
          isChoice="单选"
          @getPic="getPic"
          :gridBtn="gridBtn"
          :gridPic="gridPic"
          v-if="modalPic"
        ></uploadPictures>
      </Modal>
    </Card>
    <add-attr ref="addattr" @getList="userSearchs"></add-attr>
  </div>
</template>

<script>
import vuedraggable from "vuedraggable";
import { mapState } from "vuex";
import uploadPictures from "@/components/uploadPictures";
import addAttr from "../productAttr/addAttr";
import { shopListApi, categoryListApi, productUpdateApi, productGetInfoApi } from '@/api/coffee';
import {
  productGetRuleApi,
} from "@/api/product";
export default {
  name: "product_productAdd",
  components: {
    uploadPictures,
    addAttr,
    draggable: vuedraggable,
  },
  data() {
    return {
      type: 0,
      spinShow: false,
      openSubimit: false,
      gridPic: {
        xl: 6,
        lg: 8,
        md: 12,
        sm: 12,
        xs: 12,
      },
      gridBtn: {
        xl: 4,
        lg: 8,
        md: 8,
        sm: 8,
        xs: 8,
      },
      categoryList: [],
      shopList: [],
      // 规格模板
      selectRule: "",
      ruleList: [],
      moveIndex: "",
      manyFormValidate: [],
      // 规格数据
      formDynamic: {
        attrsName: "",
        attrsVal: "",
        price: "",
      },
      formDynamicNameData: [],
      isBtn: false,
      showIput: false,
      time:[
        '00:00:00',
        '23:59:59'
      ],
      // 规格模板结束
      formValidate: {
        id: 0,
        name: "",
        category_id: "",
        shop_id: "",
        info: "",
        img: "",
        images:"",
        price: 0,
        cost: 0,
        retail_price: 0,
        is_status: 0,
        sort: 10,
        stock: 9999,
        attrs: [],
        is_stock: 0,
        is_hotel: 1,
        is_sod: 1,
        is_activity: 1,
      },
      currentTab: "1",
      grid: {
        xl: 8,
        lg: 8,
        md: 12,
        sm: 24,
        xs: 24,
      },
      modalPic: false,
      picTit: "",
      ruleValidate: {
        name: [
          { required: true, message: "请输入商品名称", trigger: "blur" },
        ],
        shop_id: [
          { required: true, message: "请选择店铺", trigger: "blur" },
        ],
        category_id: [
          { required: true, message: "请选择分类", trigger: "blur" },
        ],
        selectRule: [
          { required: true, message: "请选择商品规格属性", trigger: "change" },
        ],
      },
      selectedImg:'',
    };
  },
  computed: {
    ...mapState("admin/layout", ["isMobile"]),
    labelWidth() {
      return this.isMobile ? undefined : 120;
    },
    labelPosition() {
      return this.isMobile ? "top" : "right";
    },
    labelBottom() {
      return this.isMobile ? undefined : 15;
    },
  },
  mounted() {
    shopListApi().then(res => {
      this.shopList = res.data.list
    })
    categoryListApi().then(res => {
      this.categoryList = res.data.list
    })
    productGetRuleApi().then((res) => {
      this.ruleList = res.data;
    });
    if (this.$route.params.id !== "0" && this.$route.params.id) {
      this.formValidate.id = this.$route.params.id
      productGetInfoApi(this.$route.params.id).then(res => {
        this.time = [
          res.data.start_time,
          res.data.end_time
        ]
        res.data.price = parseFloat(res.data.price)
        if (res.data.cost) {
          res.data.cost = parseFloat(res.data.cost)
        } else {
          res.data.cost = 0.00
        }
        res.data.retail_price = parseFloat(res.data.retail_price)
        this.formValidate = res.data;
      }).catch(function (res) {
        that.spinShow = false;
        that.$Message.error(res.msg);
      })
    }
  },
  methods: {
    end() {
      this.moveIndex = "";
    },
    // 取消
    offAttrName() {
      this.showIput = false;
    },
    // 添加规则名称
    createAttrName() {
      let reg = /^[0-9]+.?[0-9]*/;
      if (!reg.test(this.formDynamic.price)) {
        this.$Message.warning("请输入正确的价格(必须为数字)");
        return;
      }
      reg = /[\(\)（）¥]/;
      if (reg.test(this.formDynamic.attrsVal)) {
        this.$Message.warning("加料名不能设置()¥等符号");
        return;
      }
      if (this.formDynamic.attrsName && this.formDynamic.attrsVal) {
        let data = {
          value: this.formDynamic.attrsName,
          detail: [this.formDynamic.attrsVal + '(¥' + this.formDynamic.price + ')'],
        };
        this.formValidate.attrs.push(data);
        var hash = {};
        this.formValidate.attrs = this.formValidate.attrs.reduce(function (item, next) {
          /* eslint-disable */
          hash[next.value] ? "" : (hash[next.value] = true && item.push(next));
          return item;
        }, []);
        this.clearAttr();
        this.showIput = false;
      } else {
        this.$Message.warning("请添加完整的规格！");
      }
    },
    // 添加按钮
    clearAttr() {
      this.formDynamic.attrsName = "";
      this.formDynamic.attrsVal = "";
      this.formDynamic.price = "";
    },
    addBtn() {
      this.clearAttr();
      this.showIput = true;
    },
    // 添加属性
    createAttr(num, idx, price) {
      let reg = /^[0-9]+.?[0-9]*/;
      if (!reg.test(price)) {
        this.$Message.warning("请输入正确的价格(必须为数字)");
        return;
      }
      reg = /[\(\)（）¥]/;
      if (reg.test(num)) {
        this.$Message.warning("加料名不能设置()¥等符号");
        return;
      }
      if (num) {
        num = num + '(¥' + price + ')';
        this.formValidate.attrs[idx].detail.push(num);
        var hash = {};
        this.formValidate.attrs[idx].detail = this.formValidate.attrs[
          idx
        ].detail.reduce(function (item, next) {
          /* eslint-disable */
          hash[next] ? "" : (hash[next] = true && item.push(next));
          return item;
        }, []);
      } else {
        this.$Message.warning("请添加属性");
      }
    },
    // 删除规格
    handleRemoveRole(index) {
      this.formValidate.attrs.splice(index, 1);
      this.manyFormValidate.splice(index, 1);
    },
    // 删除属性
    handleRemove2(item, index) {
      item.splice(index, 1);
    },
    // 属性移除
    checkMove(evt) {
      console.log(evt);
      this.moveIndex = evt.draggedContext.index;
    },
    // 属性弹窗回调函数；
    userSearchs() {
      productGetRuleApi().then((res) => {
        this.ruleList = res.data;
      });
    },
    // 添加规则；
    addRule() {
      this.$refs.addattr.modal = true;
    },
    // 规格模板 提交
    confirm() {
      let that = this;
      if (that.selectRule.trim().length <= 0) {
        return that.$Message.error("请选择属性");
      }
      that.ruleList.forEach(function (item, index) {
        if (item.rule_name === that.selectRule) {
          that.formValidate.attrs = item.rule_value;
        }
      });
    },
    // 店铺切换
    shopChange() {
      this.formValidate.category_id = ''
      categoryListApi({ shop_id: this.formValidate.shop_id }).then(res => {
        this.categoryList = res.data.list
      })
    },
    // tab切换
    onhangeTab(name) {
      this.currentTab = name;
    },
    // 点击商品图
    modalPicTap(index) {
      this.selectedImg = index
      this.modalPic = true;
    },
    // 获取单张图片信息
    getPic(pc) {
      this.formValidate[this.selectedImg] = pc.att_dir;
      this.modalPic = false;
    },
    // 提交
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.openSubimit = false;
          this.formValidate.start_time = this.time[0];
          this.formValidate.end_time = this.time[1];
          productUpdateApi(this.formValidate)
            .then(async (res) => {
              this.openSubimit = true;
              this.$Message.success(res.msg);
              if (this.$route.params.id === "0") {
                cacheDelete().catch((err) => {
                  this.$Message.error(err.msg);
                });
              }
              setTimeout(() => {
                this.$router.push({ path: "/admin/coffee/product_list" });
              }, 500);
            })
            .catch((res) => {
              this.openSubimit = false;
              this.$Message.error(res.msg);
            });
        } else {
          if (!this.formValidate.name) {
            return this.$Message.warning("商品信息-商品名称不能为空");
          } else if (!this.formValidate.shop_id) {
            return this.$Message.warning("商品信息-店铺不能为空");
          } else if (!this.formValidate.category_id) {
            return this.$Message.warning("商品信息-分类不能为空");
          }
        }
      });
    },
    // 表单验证
    validate(prop, status, error) {
      if (status === false) {
        this.$Message.warning(error);
      }
    },
  },
};
</script>
<style scoped lang="stylus">
.ivu-checkbox-wrapper{
    margin-right 19px;
}
.list-group {
  margin-left: -8px;
}

.borderStyle {
  border: 1px solid #ccc;
  padding: 8px;
  border-radius: 4px;
}

.drag {
  cursor: move;
}

.move-icon {
  width: 30px;
  cursor: move;
  margin-right: 10px;
}

.move-icon .icondrag2 {
  font-size: 26px;
  color: #d8d8d8;
}

.maxW /deep/.ivu-select-dropdown {
  max-width: 600px;
}

#shopp-manager .ivu-table-wrapper {
  border-left: 1px solid #dcdee2;
  border-top: 1px solid #dcdee2;
}

.noLeft {
  >>> .ivu-form-item-content {
    margin-left: 0 !important;
  }
}

#shopp-manager .ivu-form-item {
  position: relative;
}

#shopp-manager .ivu-form-item .tips {
  position: absolute;
  color: #999;
  top: 29px;
  left: -77px;
  font-size: 12px;
}

.iview-video-style {
  width: 40%;
  height: 180px;
  border-radius: 10px;
  background-color: #707070;
  margin-top: 10px;
  position: relative;
  overflow: hidden;
}

.iview-video-style .iconv {
  color: #fff;
  line-height: 180px;
  width: 50px;
  height: 50px;
  display: inherit;
  font-size: 26px;
  position: absolute;
  top: -74px;
  left: 50%;
  margin-left: -25px;
}

.iview-video-style .mark {
  position: absolute;
  width: 100%;
  height: 30px;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  text-align: center;
}

.uploadVideo {
  margin-left: 10px;
}

.submission {
  margin-left: 10px;
}

.color-list .tip {
  color: #c9c9c9;
}

.color-list .color-item {
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  color: #fff;
  margin-right: 10px;
}

.color-list .color-item.blue {
  background-color: #1E9FFF;
}

.color-list .color-item.yellow {
  background-color: rgb(254, 185, 0);
}

.color-list .color-item.green {
  background-color: #009688;
}

.color-list .color-item.red {
  background-color: #ed4014;
}

.columnsBox {
  margin-right: 10px;
}

.priceBox {
  width: 100%;
}

.rulesBox {
  display: flex;
  flex-wrap: wrap;
}

.pictrueBox {
  display: inline-block;
}

.pictrueTab {
  width: 40px !important;
  height: 40px !important;
}

.pictrue {
  width: 60px;
  height: 60px;
  border: 1px dotted rgba(0, 0, 0, 0.1);
  margin-right: 15px;
	margin-bottom 10px;
  display: inline-block;
  position: relative;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
  }

  .btndel {
    position: absolute;
    z-index: 1;
    width: 20px !important;
    height: 20px !important;
    left: 46px;
    top: -4px;
  }
}

.upLoad {
  width: 58px;
  height: 58px;
  line-height: 58px;
  border: 1px dotted rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.02);
  cursor: pointer;
}

.curs {
  cursor: pointer;
}

.inpWith {
  width: 60%;
}

.labeltop {
  >>> .ivu-form-item-label {
    float: none !important;
    display: inline-block !important;
    margin-left: 120px !important;
    width: auto !important;
  }
}

.video-icon {
  background-image: url('https://cdn.oss.9gt.net/prov1.1/1/icons.png'); // cdn.oss.9gt.net/prov1.1/1/icons.png);
  // background-color: #fff;
  background-position: -9999px;
  background-repeat: no-repeat;
}
</style>
