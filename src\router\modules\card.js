import BasicLayout from "@/layouts/basic-layout";

const pre = "card"; // 前缀

export default {
    path: "/admin/card",
    name: "card", // 当前路由的name
    header: "card",
    meta: {
        // 授权标识
        auth: ["admin-index-index"],
    },
    redirect: {
        name: `${pre}_index`,
    },
    component: BasicLayout,
    children: [
        {
            path: "index", // redirect path
            name: `${pre}index`,
            meta: {
                title: "交易统计",
                auth: ["admin-index-index"],
            },
            component: () => import("@/pages/card/index"),
        },
        {
            path: "config", // redirect path
            name: `${pre}config`,
            meta: {
                title: "店铺设置",
                auth: ["admin-index-index"],
            },
            component: () => import("@/pages/card/config"),
        },
        {
            path: "user", // redirect path
            name: `${pre}user`,
            meta: {
                title: "用户管理",
                auth: ["admin-index-index"],
            },
            component: () => import("@/pages/card/user"),
        },
        {
            path: "menu", // redirect path
            name: `${pre}menu`,
            meta: {
                title: "分类管理",
                auth: ["admin-index-index"],
            },
            component: () => import("@/pages/card/menu"),
        },
        {
            path: "product", // redirect path
            name: `${pre}product`,
            meta: {
                title: "商品管理",
                auth: ["admin-index-index"],
            },
            component: () => import("@/pages/card/product"),
        },
        {
            path: "add_product/:id?",
            name: `${pre}productAdd`,
            meta: {
                title: "商品添加",
                auth: ["admin-index-index"],
            },
            component: () => import("@/pages/card/product/addForm"),
        },
        {
            path: "product_attr",
            name: `${pre}productAttr`,
            meta: {
                title: "商品规格",
                auth: ["admin-index-index"],
            },
            component: () => import("@/pages/card/productAttr"),
        },
        {
            path: "order", // redirect path
            name: `${pre}order`,
            meta: {
                title: "订单管理",
                auth: ["admin-index-index"],
            },
            component: () => import("@/pages/card/order"),
        },
        {
            path: "finance", // redirect path
            name: `${pre}finance`,
            meta: {
                title: "余额记录",
                auth: ["admin-index-index"],
            },
            component: () => import("@/pages/card/finance"),
        },
        {
            path: "cashier", // redirect path
            name: `${pre}cashier`,
            meta: {
                title: "收银台",
                auth: ["admin-index-index"],
            },
            component: () => import("@/pages/card/cashier"),
        },
    ],
};
