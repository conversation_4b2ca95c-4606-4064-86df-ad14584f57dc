<template>
	<div class="list">
		<div class="item" :class="{on:parentCur == index}" v-for="(nav,index) in filterSider" :key="index"
			@click="handelParentClick(nav,index)">{{nav.title}}</div>
	</div>
</template>
<script>
	import {
		mapGetters
	} from "vuex";
	export default {
		name: "headerNav",
		props: {},
		data() {
			return {
				parentCur: 0,
			};
		},
		computed: {
			...mapGetters("admin/menu", ["filterSider"]),
		},
		watch: {
			$route: {
				handler(n) {
					let that = this;
					let activeLink = n.path;
					let storage=window.localStorage;
					storage.setItem('chiidLink',activeLink);
					let funLink = function (children,index) {
						children.forEach((item)=>{
							if(activeLink == item.path){
							    that.parentCur = index
									storage.setItem('parentLink',that.filterSider[index].path)
							    that.$store.commit('admin/layout/setParentCur', index);
									if (that.filterSider[index].hasOwnProperty('children')) {
										storage.setItem('isChildren', true);
									} else {
										storage.setItem('isChildren', false);
									}
									return false;
							}
							if(item.children){
								funLink(item.children,index);
							}
						})
					}
					that.filterSider.forEach((item,index)=>{
						  if(item.children){
						  	funLink(item.children,index);
						  }else{
								if(activeLink == item.path){
								    that.parentCur = index
								    that.$store.commit('admin/layout/setParentCur', index);
										that.$store.commit('admin/layout/setChildren', false);
										return false;
								}
							}
					})
				},
				immediate: true,
			}
		},
		mounted() {
			this.activeMenu();
		},
		methods: {
			isChildren(){
				if (this.filterSider[0].hasOwnProperty('children')) {
					this.$store.commit('admin/layout/setChildren', true);
				} else {
					this.$store.commit('admin/layout/setChildren', false);
				}
			},
			activeMenu() {
				let storage = window.localStorage;
				let activeLink = storage.getItem('parentLink');
				let chiidLink = storage.getItem('chiidLink');
				if (storage.getItem('isChildren') == null || activeLink == this.filterSider[0].path) {
					this.isChildren();
				} else {
					this.$store.commit('admin/layout/setChildren', JSON.parse(storage.getItem('isChildren')));
					this.filterSider.forEach((item, index) => {
						if (activeLink == item.path) {
							this.parentCur = index
							this.$store.commit('admin/layout/setParentCur', index);
						}
					})
					this.$router.push({
						path: chiidLink
					})
				}
			},
			handelParentClick(nav, index) {
				this.parentCur = index;
				this.$store.commit('admin/layout/setParentCur', index);
				let chiidLink = '';
				let storage = window.localStorage;
				if (nav.children) {
					if (nav.children[0].children) {
						var recursiveFunction = function() {
							const getStr = function(list) {
								list.children.forEach(function(row, index) {
									if (list.children[0].children) {
										getStr(list.children[0])
									} else {
										if (index == 0) return chiidLink = row.path
									}
								})
							}
							getStr(nav.children[0])
						}
						recursiveFunction()
					} else {
						chiidLink = nav.children[0].path
					}
					this.$store.commit('admin/layout/setChildren', true);
					storage.setItem('isChildren', true);
				} else {
					chiidLink = nav.path
					this.$store.commit('admin/layout/setChildren', false);
					storage.setItem('isChildren', false);
				}
				this.$router.push({
					path: chiidLink
				})
				storage.setItem('parentLink', nav.path);
				storage.setItem('chiidLink', chiidLink);
			}
		},
	};
</script>
<style scoped lang="stylus">
	@media only screen and (max-width: 1190px) {
		.list .item {
			margin 0 14px !important;
		}
	}
	
	@media only screen and (max-width: 1153px) {
		.list .item {
			margin 0 12px !important;
		}
	}
	
	@media only screen and (max-width: 1115px) {
		.list .item {
			margin 0 9px !important;
		}
	}
	
	@media only screen and (max-width: 1060px) {
		.list .item {
			margin 0 8px !important;
		}
	}
	
	@media only screen and (max-width: 1043px) {
		.list .item {
			margin 0 6px !important;
		}
	}
	
	@media only screen and (max-width: 1000px) {
		.list .item {
			margin 0 5px !important;
		}
	}
	
	@media only screen and (max-width: 973px) {
		.list .item {
			margin 0 3px !important;
		}
	}
	
	@media only screen and (max-width: 933px) {
		.list .item {
			margin 0 1px !important;
		}
	}

	.list {
		cursor pointer;
		display inline-block;

		.item {
			position relative;
			font-weight 400;
			display inline-block;
			margin 0 16px;
			transition: all .3s;

			&:after {
				content: ' ';
				position: absolute;
				left: 0;
				bottom: 0;
				width: 100%;
				height: 3px;
				background: transparent;
			}

			&:hover {
				font-weight 600;

				&:after {
					background: #fff;
				}
			}

			&.on {
				font-weight 600;

				&:after {
					background: #fff;
				}
			}
		}
	}
</style>
