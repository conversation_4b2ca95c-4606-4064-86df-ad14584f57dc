<template>
	<div>
		<div class="goodsCard acea-row row-between">
			<div class="conter">
				<Card :bordered="false" dis-hover class="ivu-mt cart" :style="'height:' + clientHeight + 'px;'">
					<div class="acea-row row-between row-bottom">
						<div class="left">
							<div class="title acea-row row-between-wrapper">
								<router-link :to="{ path: '/admin/singleCoffee/order' }">
									<Button icon="ios-arrow-back" size="small" class="mr20">返回订单页</Button>
								</router-link>
								<div class="text">结账清单</div>
							</div>
							<div class="listCon" :style="'height:' + cartHeight + 'px;'">
								<div class="list">
									<div class="item acea-row row-middle" v-for="(item,index) in cartList" :key="index">
										<div class="pictrue">
											<img :src="item.img" />
										</div>
										<div class="text">
											<div class="name line1">{{ item.name }}</div>
											<div
												class="info"
												v-if="item.product_attr"
											>
												{{ item.product_attr }}
											</div>
											<div class="info" v-else>默认</div>
											<div>¥ {{ item.pay_price }}</div>
										</div>
										<div class="del" @click="delCart(item, index)">删除</div>

										<div class="cartBnt acea-row row-center-wrapper">
											<InputNumber
												:min="1"
												v-model="item.num"
												:editable="true"
												size="small"
												controls-outside
												@on-change="cartChange(item)"
											></InputNumber>
										</div>
									</div>
								</div>
								<div
									class="noCart acea-row row-center-wrapper"
									v-if="!cartList.length"
								>
									<div>
										<div class="pictrue">
											<img src="@/assets/images/no-thing.png" />
										</div>
										<div class="tip">购物车暂时无商品</div>
									</div>
								</div>
							</div>
							<div class="truePrice">
								实付：¥
								<span class="num">{{ pay_price }}</span>
							</div>
							<div class="footer">
								<div class="conInfo acea-row row-between-wrapper">
									<div>
										共计 {{ cartSum }} 件，已优惠
										¥0
									</div>
									<div class="detailed" @click="discountCon" v-if="cartList.length">优惠明细 ></div>
								</div>
								<div class="pay acea-row row-between-wrapper" v-if="cartList.length">
									<div class="bnt" @click="payPrice('offline')">园区卡收款</div>
									<div class="bnt" @click="payPrice('weixin')">POS扫码付款</div>
									<!-- <div class="bnt on" @click="yuPay('yue')">余额收款</div> -->
								</div>
								<div class="pay noCart acea-row row-between-wrapper" v-else>
									<div class="bnt">园区卡收款</div>
									<div class="bnt">POS扫码付款</div>
									<!-- <div class="bnt on" >余额收款</div> -->
								</div>
							</div>
						</div>
						<div class="right">
							<div class="item" @click="delAll" v-if="cartList.length || invalidList.length">
								<div>整单</div>取消
							</div>
							<div class="noCart" v-else>
								<div class="item">
									<div>整单</div>取消
								</div>
							</div>
		<!-- 					<div class="item" @click="credit" v-if="cartList.length || invalidList.length">
								<div>挂账</div>
							</div>
							<div class="noCart" v-else>
								<div class="item">
									<div>挂账</div>
								</div>
							</div> -->
						</div>
					</div>
				</Card>
			</div>
			<Spin size="large" fix v-if="spinShow"></Spin>
			<div class="goods">
				<Card :bordered="false" dis-hover class="ivu-mt" :style="'height:' + clientHeight + 'px;'">
					<Tabs :animated="false" v-model="currentTab">
						<TabPane label="商品" name="1"></TabPane>
					</Tabs>
					<div class="acea-row row-between" v-if="currentTab == 1">
						<div class="goodsCon">
							<Input
								v-model="formValidate.store_name"
								search
								enter-button
								placeholder="搜索商品名称"
								element-id="name"
								@on-search="orderSearch"
								class="input"
							></Input>
							<div class="list acea-row" v-if="goodData.length" :style="'height:' + goodsHeight + 'px;'">
								<div class="item" v-for="(item,index) in goodData" :key="index" @click="attrTap(item)">
									<div class="pictrue">
										<img :src="item.img" />
									</div>
									<div class="text">
										<div class="name line1">{{ item.name }}</div>
										<div class="money">¥ {{ item.price }}</div>
										<div class="iconfont iconxuanzhong6" v-if="item.cart_num"></div>
									</div>
								</div>
							</div>
							<div class="noGood acea-row row-center-wrapper" v-else>
								<div>
									<div class="pictrue">
										<img src="@/assets/images/no-thing.png" />
									</div>
									<div class="tip">暂时无商品</div>
								</div>
							</div>
							<div class="acea-row row-right page" v-if="goodData.length">
								<Page
									:total="total"
									show-elevator
									show-total
									@on-change="pageChange"
									:page-size="formValidate.limit"
								/>
							</div>
						</div>
						<div
							class="goodClass acea-row row-center"
							:style="'height:' + (Number(goodsHeight) + 87) + 'px;'"
						>
							<div>
								<div
									class="item line1"
									:class="currentCate == index ? 'on' : ''"
									v-for="(item,index) in cateData"
									:key="index"
									@click="cateTap(item, index)"
								>{{ item.name }}</div>
							</div>
						</div>
					</div>
				</Card>
			</div>
		</div>

		<Modal v-model="modal" title="备注" footer-hide>
			<form-create v-model="fapi" :rule="rule" @on-submit="onSubmit" class="remark"></form-create>
		</Modal>
		<productAttr
			ref="attrs"
			:attr="attr"
			:disabled="disabled"
			:isCart="isCart"
			@getCartList="getCartList"
		></productAttr>
		<Modal
			v-model="modalUser"
			title="用户列表"
			footerHide
			class="paymentFooter"
			scrollable
			width="900"
			@on-cancel="cancelUserInfo"
		>
			<userInfo ref="goodslist" 
			:pay_price="pay_price"
			:shop_id="this.formValidate.shop_id"
			@closeUser="closeUser"
			 v-if="modalUser">
			</userInfo>
		</Modal>
	</div>
</template>

<script>
// import userList from '@/components/userList'
// import storeList from '@/components/storeList'
// import couponList from '@/components/couponList'
import productAttr from './components/productAttr'
import userInfo from './components/user'
var that
import {
	categoryProductList,
	productAttrInfo,
	categoryListApi,
	productListApi,
	cashierCreateOrder,
	cashierCartList,
	cashierCartDel,
	cashierCartNum,
	cashierPay,
} from '@/api/coffee';
// import {
// 	checkOrderApi
// } from '@/api/user';
export default {
	name: 'index',
	components: {
		productAttr,
		userInfo
	},
	data() {
		return {
			spinShow:false,
			// 新
			formValidate: {
				shop_id: '',
				category_id: '',
				store_name: '',
				type: 0,
				page: 1,
				limit: 30,
			},
			pay_price:0,
			modalUser: false,
			userId:'',
			// 旧
			flag: true,
			total: 0,
			goodData: [],
			cateData: [],
			currentCate: 0, //分类的当前index；
			currentTab: '1',
			codeNum: '',
			payNum: '',
			userInfo: {},
			storeInfos: {}, //门店店员信息
			storeList: [], //门店列表
			attr: {
				productAttr: [],
				productSelect: {}
			},
			storeInfo: {}, //商品 信息
			productValue: [],
			attrValue: '', //已选属性
			productId: 0, //产品id
			cartList: [],
			isCart: 0,
			cartInfo: { //更改属性所需参数
				cart_id: 0,
				product_id: 0,
				unique: ''
			},
			modal: false,
			fapi: {},
			rule: [{
				type: "input",
				field: "remarks",
				title: "备注",
				props: {
					type: "textarea",
					maxlength: 100,
					'show-word-limit': true
				},
			}],
			modal2: false,
			fapi2: {},
			rule2: [{
				type: "InputNumber",
				field: "change_price",
				title: "实付款",
				value: 0,
				props: {
					min: 0,
				},
			}],
			integral: false, //是否使用积分
			coupon: false, //是否使用优惠券
			couponId: 0,//优惠券id
			modalPay: false,
			cartSum: 0,
			priceInfo: {},
			createOrder: {
				remarks: '',
				change_price: 0,
				cart_id: [], // 购物车id
				userCode: '',
				is_price: 0
			},
			modalCash: false,
			numList: ['7', '8', '9', '4', '5', '6', '1', '2', '3', '0', '.'],
			collectionArray: [],
			collection: 0,
			isOrderCreate: 0,
			discount: false,
			payTape: '', // 支付方式
			orderId: '', //订单id
			clientHeight: 0,
			cartHeight: 0,
			goodsHeight: 0,
			invalidList: [],
			disabled: false //阻止属性弹窗多次提交
		}
	},
	created() {
    this.formValidate.shop_id = this.$store.state.admin.user.info.shop_id
		that = this
    this.getList()
	},
	mounted() {
		this.$nextTick(() => {
			this.clientHeight = `${document.documentElement.clientHeight}` - 80; //获取浏览器可视区域高度
			this.cartHeight = `${document.documentElement.clientHeight}` - 340;
			this.goodsHeight = `${document.documentElement.clientHeight}` - 250;
			let that = this;
			window.onresize = function () {
				that.clientHeight = `${document.documentElement.clientHeight}` - 80;
				that.cartHeight = `${document.documentElement.clientHeight}` - 340;
				that.goodsHeight = `${document.documentElement.clientHeight}` - 250;
			}
		});
	},
	methods: {
		// 挂账方法
		credit() {
			this.spinShow = true
			cashierPay({type:'credit',userId:this.formValidate.shop_id}).then(res => {
				this.spinShow = false
				this.$Message.success(res.msg)
				this.getCartList();
			}).catch(err => {
				this.spinShow = false
				this.$Message.error(err.msg)
			})
		},
		closeUser() {
			this.modalUser = false;
			this.getCartList();
		},
		getUserId(userId) {
			this.modals = false;
			this.userId = userId
			console.log(this.userId)
		},
		cancelUserInfo() {
			this.modalUser = false;
		},
		yuPay() {
			this.modalUser = true
		},
		payPrice(payTape) {
			this.spinShow = true
			cashierPay({type:payTape,userId:this.formValidate.shop_id}).then(res => {
				this.spinShow = false
				this.$Message.success(res.msg)
				this.getCartList();
			}).catch(err => {
				this.spinShow = false
				this.$Message.error(err.msg)
			})
		},
		// 购物车加减
		cartChange(item) {
			let data = {
				number: item.num,
				id: item.id
			}
			console.log(data)
			cashierCartNum(data).then(res => {
				this.getCartList();
			}).catch(err => {
				this.$Message.error(err.msg)
			})
		},
		//计算金额
		cartCompute() {
			let ids = []
			let num = 0
			let pay_price = 0
			this.cartList.forEach(item => {
				ids.push(item.id)
				num += Number(item.num)
				pay_price += Number(item.pay_price)
			})
			this.cartSum = num
			this.pay_price = pay_price
			this.createOrder.cart_id = ids;
		},
		// 购物车列表
		getCartList() {
			let uid = this.formValidate.shop_id;
			if (uid > 0) {
				cashierCartList(uid).then(res => {
					this.cartList = res.data;
					this.cartCompute();
				})
			} else {
				this.$Message.error('请选择门店');
			}
		},
		// 选择属性
		attrTap(item) {
			this.disabled = false;
			// 查看商品详情
			productAttrInfo(item.id).then(res => {
				let data = res.data;
				if (data.attrs.length > 0) {
					this.$refs.attrs.modals = true;
					this.$refs.attrs.info(data);
					this.attr = data;
				} else {
					cashierCreateOrder({ product_id: item.id, price: item.price, shop_id: item.shop_id }).then(res => {
						this.$Message.success(res.msg)
						this.modals = false;
						this.getCartList()
					}).catch(err => {
						this.$Message.error(err.msg)
					})
				}
			})
			// if (this.userInfo && this.userInfo.uid > 0) {
			// 	this.productId = item.product_id;
			// 	let specType = item.product.spec_type;
			// 	if (specType) {
			// 		this.isCart = 0; //判断切换属性或是加入购物车：0加入购物车；1切换属性
			// 		this.$refs.attrs.modals = true;
			// 		this.goodsInfo(item.product_id);
			// 	} else {
			// 		// 0为单规格属性
			// 		this.joinCart(0);
			// 	}
			// } else {
			// 	this.$Message.error('请添加或选择用户')
			// }
		},
		//点击分类
		cateTap(item, index) {
			this.currentCate = index;
			this.formValidate.category_id = item.id;
			this.formValidate.page = 1;
			productListApi(this.formValidate).then(res => {
				this.goodData = res.data.list
				this.total = res.data.count
			})
		},
		//搜索
		orderSearch() {
			this.formValidate.page = 1;
			productListApi(this.formValidate).then(res => {
				this.goodData = res.data.list
				this.total = res.data.count
			})
		},
		pageChange(e) {
			this.formValidate.page = e;
			productListApi(this.formValidate).then(res => {
				this.goodData = res.data.list
				this.total = res.data.count
			})
		},
		getList() {
			this.clear();
			this.loading = true
			productListApi(this.formValidate).then(res => {
				this.goodData = res.data.list
				this.total = res.data.count
			})
			categoryListApi({ shop_id: this.formValidate.shop_id, is_show:10 }).then(res => {
				let all = {
					name: '全部商品',
					id: ''
				};
				res.data.list.unshift(all)
				this.cateData = res.data.list
			})
			this.getCartList()
		},
		// 商品信息获取
		categoryProductList() {
			categoryProductList().then(res => {
				console.log(res)
			}).catch(err => {
				this.$Message.error(err.msg)
			})
		},
		clear() {
			this.priceInfo.couponPrice = 0;
			this.priceInfo.payPrice = 0;
			this.priceInfo.deductionPrice = 0;
			this.priceInfo.totalPrice = 0;
			this.priceInfo.vipPrice = 0;
			this.cartList = [];
			this.invalidList = [];
			this.cartSum = 0;
			this.collection = 0;
			this.collectionArray = [];
			this.createOrder.change_price = 0;
			this.createOrder.remarks = '';
			this.coupon = false;
			this.couponId = 0;
			this.integral = false;
			this.createOrder.is_price = 0;
		},
		cancel() {
			this.collection = 0;
			this.collectionArray = [];
		},
		//点击出现优惠明细
		discountCon() {
			this.discount = true;
		},
		//现金收款创建订单并支付
		cashBnt() {
			if (this.isOrderCreate) {
				this.getCashierPay();
			} else {
				this.orderCreate();
			}
		},
		//清除计算机输入的数字
		delNum() {
			this.collectionArray.pop();
			this.collection = this.collectionArray.length ? this.collectionArray.join("") : 0;
		},
		//输入实际收款金额
		numTap(item) {
			this.collectionArray.push(item);
			this.collection = this.collectionArray.join("");
		},
		confirmOrder() {
			let data = {
				order_id: this.orderId
			}
			checkOrderApi(3, data).then(res => {
				if (res.data.status) {
					this.isOrderCreate = 0;
					this.$Message.success('支付成功');
					this.userInfo = null;
					this.goodList();
					this.modalPay = false;
					this.clear();
					let storage = window.localStorage;
					storage.removeItem("cashierUser");
				} else {
					this.$Message.success('未支付');
				}
			}).catch(err => {
				this.$Message.error(err.msg)
			})
		},
		// 线上支付和余额支付
		confirm() {
			this.createOrder.userCode = this.payNum;
			if (this.payTape == 'yue') {
				if (!this.createOrder.userCode) {
					return this.$Message.error("请扫描个人中心二维码")
				}
			}
			this.orderCreate();
		},
		getCashierPay() {
			let data = {
				payType: 'cash'
			}
			if (parseFloat(this.priceInfo.payPrice) > parseFloat(this.collection)) {
				return this.$Message.error("您付款金额不足")
			}
			cashierPay(this.orderId, data).then(res => {
				this.isOrderCreate = 0;
				this.$Message.success('支付成功')
				this.modalCash = false;
				this.clear();
				let storage = window.localStorage;
				storage.removeItem("cashierUser");
				this.userInfo = null;
				this.goodList();
			}).catch(err => {
				this.$Message.error(err.msg)
			})
		},
		// 创建订单
		orderCreate() {
			if (this.payTape == 'cash') {
				if (parseFloat(this.priceInfo.payPrice) > parseFloat(this.collection)) {
					return this.$Message.error("您付款金额不足")
				}
			}
			cashierCreate(this.userInfo.uid, this.createOrder).then(res => {
				let storage = window.localStorage;
				if (this.payTape == 'yue') {
					this.payNum = '';
					this.createOrder.userCode = '';
					if (res.data.status == 'ORDER_CREATE') {
						this.isOrderCreate = 1;
						this.orderId = res.data.result.order_id;
						this.$Message.error(res.msg);
					} else {
						this.isOrderCreate = 0;
						this.$Message.success('支付成功');
						storage.removeItem("cashierUser");
						this.userInfo = null;
						this.goodList();
						this.modalPay = false;
						this.clear();
					}
				}
				if (this.payTape == 'cash') {
					if (res.data.status == 'PAY_SUCCESS') {
						this.$Message.success('支付成功');
						storage.removeItem("cashierUser");
						this.userInfo = null;
						this.goodList();
						this.modalCash = false;
						this.clear();
					}
				}
				if (this.payTape == '') {
					if (res.data.status == 'ORDER_CREATE') {
						this.isOrderCreate = 1;
						this.orderId = res.data.result.order_id;
						this.$Message.success('创建成功');
					}
				}
			}).catch(err => {
				this.payNum = '';
				this.$Message.error(err.msg);
			})
		},
		// 点击使用优惠券
		couponTap() {
			this.$refs.coupon.modals = true;
			this.$refs.coupon.currentid = 0;
			this.$refs.coupon.getList();
		},
		getCouponId(e) {
			this.couponId = e.id;
			this.coupon = true;
			this.cartCompute();
		},
		closeCoupon() {
			this.coupon = false;
			this.couponId = 0;
			this.cartCompute();
		},
		// 是否使用积分
		integralTap() {
			this.integral = !this.integral
			this.cartCompute();
		},
		changePrice() {
			this.fapi2.resetFields();
			this.modal2 = true;
		},
		remarks() {
			// this.fapi.resetFields();
			this.modal = true;
		},
		// 提交备注
		onSubmit(formData) {
			this.createOrder.remarks = formData.remarks;
			// this.fapi.resetFields();
			this.modal = false;
		},
		onSubmit2(formData) {
			if (formData.change_price >= 0 && formData.change_price != null) {
				this.priceInfo.payPrice = formData.change_price;
				this.$Message.success('改价成功');
				this.createOrder.is_price = 1;
				this.createOrder.change_price = formData.change_price;
				this.fapi2.resetFields();
				this.modal2 = false;
			} else {
				return this.$Message.error('价格不能为空')
			}
		},
		// 删除
		del(ids, type, index, num) {
			this.$Modal.confirm({
				title: '删除该购物车',
				content: '<p>确定要删除该购物车吗？</p><p>删除该购物车后将无法恢复，请谨慎操作！</p>',
				onOk: () => {
					cashierCartDel(ids).then(res => {
						this.$Message.success('删除成功');
						this.getCartList()
						// this.goodList();
						// if (type) {
						// 	this.clear();
						// 	this.invalidList = [];
						// } else {
						// 	if (num) {
						// 		this.invalidList.splice(index, 1);
						// 	} else {
						// 		this.cartList.splice(index, 1);
						// 		if (this.cartList.length) {
						// 			this.cartCompute();
						// 		} else {
						// 			this.clear();
						// 		}
						// 	}
						// 	// this.cartSum = this.cartSum - 1;
						// }
					}).catch(err => {
						this.$Message.error(err.msg)
					})
				},
				onCancel: () => { }
			});
		},
		delAll() {
			let ids = []
			this.cartList.forEach(item => {
				ids.push(item.id)
			})
			this.del({
				'ids': ids
			}, 1)
		},
		delCart(item, index, num) {
			let ids = [];
			ids.push(item.id)
			this.del({
				'ids': ids
			}, 0, index, num)
		},
		// 加入购物车
		joinCart(num) {
			let that = this;
			if (num) {
				let productSelect = that.productValue[this.attrValue];
				//如果有属性,没有选择,提示用户选择
				if (
					that.attr.productAttr.length &&
					productSelect === undefined
				) {
					return this.$Message.warning('产品库存不足，请选择其它');
				}
			}
			let uid = this.userInfo.uid;
			let data = {
				productId: this.productId,
				cartNum: 1,
				uniqueId: num ? (this.attr.productSelect !== undefined ? this.attr.productSelect.unique : "") : '',
				staff_id: this.storeInfos.id
			}
			cashierCart(uid, data).then(res => {
				this.$refs.attrs.modals = false
				this.$Message.success('添加购物车成功');
				this.getCartList();
				this.goodList();
				this.disabled = true;
			}).catch(err => {
				this.$Message.error(err.msg)
			})
		},
		changeCartAttr() {
			this.cartInfo.unique = this.attr.productSelect !== undefined ? this.attr.productSelect.unique : "";
			cashierchangeCart(this.cartInfo).then(res => {
				this.disabled = true;
				this.$Message.success(res.msg);
				this.$refs.attrs.modals = false;
				this.getCartList();
				// this.cartList.forEach(res => {
				// 	if (res.id == this.cartInfo.cart_id) {
				// 		res.productInfo.attrInfo.suk = this.attrValue;
				// 		res.truePrice = this.attr.productSelect.price;
				// 	}
				// })
				this.cartCompute();
			}).catch(err => {
				this.$Message.error(err.msg)
			})
		},
		goCat(e) {
			if (e) {
				this.changeCartAttr();
			} else {
				this.joinCart(1);
			}
		},
		// 商品详情
		goodsInfo(id) {
			cashierDetail(id, this.userInfo.uid).then(res => {
				let data = res.data;
				this.storeInfo = data.storeInfo;
				this.productValue = data.productValue;
				this.$set(this.attr, 'productAttr', data.productAttr);
				this.DefaultSelect();
			}).catch(err => {
				this.$Message.error(err.msg)
			})
		},
		/**
		 * 默认选中属性
		 * 
		 */
		DefaultSelect: function () {
			let productAttr = this.attr.productAttr;
			let value = [];
			for (var key in this.productValue) {
				if (this.productValue[key].stock > 0) {
					value = this.attr.productAttr.length ? key.split(",") : [];
					break;
				}
			}
			//isCart 1为触发购物车 0为商品
			if (this.isCart) {
				//购物车默认打开时，随着选中的属性改变
				let attrValue = [];
				this.cartList.forEach(res => {
					if (res.id == this.cartInfo.cart_id) {
						attrValue = res.productInfo.attrInfo.suk.split(",");
					}
				})
				for (let i = 0; i < productAttr.length; i++) {
					this.$set(productAttr[i], "index", attrValue[i]);
				}
			} else {
				for (let i = 0; i < productAttr.length; i++) {
					this.$set(productAttr[i], "index", value[i]);
				}
			}
			//sort();排序函数:数字-英文-汉字；
			let productSelect = this.productValue[value.join(",")];
			if (productSelect && productAttr.length) {
				this.$set(
					this.attr.productSelect,
					"store_name",
					this.storeInfo.store_name
				);
				this.$set(this.attr.productSelect, "image", productSelect.image);
				this.$set(this.attr.productSelect, "price", productSelect.price);
				this.$set(this.attr.productSelect, "stock", productSelect.stock);
				this.$set(this.attr.productSelect, "unique", productSelect.unique);
				this.$set(this.attr.productSelect, "cart_num", 1);
				this.$set(this, "attrValue", value.join(","));
			} else if (!productSelect && productAttr.length) {
				this.$set(
					this.attr.productSelect,
					"store_name",
					this.storeInfo.store_name
				);
				this.$set(this.attr.productSelect, "image", this.storeInfo.image);
				this.$set(this.attr.productSelect, "price", this.storeInfo.price);
				this.$set(this.attr.productSelect, "stock", 0);
				this.$set(this.attr.productSelect, "unique", "");
				this.$set(this.attr.productSelect, "cart_num", 0);
				this.$set(this, "attrValue", "");
			} else if (!productSelect && !productAttr.length) {
				this.$set(
					this.attr.productSelect,
					"store_name",
					this.storeInfo.store_name
				);
				this.$set(this.attr.productSelect, "image", this.storeInfo.image);
				this.$set(this.attr.productSelect, "price", this.storeInfo.price);
				this.$set(this.attr.productSelect, "stock", this.storeInfo.stock);
				this.$set(
					this.attr.productSelect,
					"unique",
					this.storeInfo.unique || ""
				);
				this.$set(this.attr.productSelect, "cart_num", 1);
				this.$set(this, "attrValue", "");
			}
		},
		storeTap() {
			this.$refs.store.modals = true
			this.$refs.store.cancel();
		},
		// 监听键盘函数
		keyboard() {
			let that = this;
			function delNums(item) {
				that.collectionArray.pop();
				that.collection = that.collectionArray.length ? that.collectionArray.join("") : 0;
			};
			function numTaps(item) {
				that.collectionArray.push(item);
				that.collection = that.collectionArray.join("");
			};
			document.onkeydown = function (event) {
				let e = event || window.event;
				let key = e.keyCode;
				if (that.modalCash) {
					event.stopPropagation(); // 阻止事件冒泡传递
					event.preventDefault(); //阻止默认事件原有功能
				}
				switch (key) {
					case 96:
						numTaps(0)
						break;
					case 97:
						numTaps(1)
						break;
					case 98:
						numTaps(2)
						break;
					case 99:
						numTaps(3)
						break;
					case 100:
						numTaps(4)
						break;
					case 101:
						numTaps(5)
						break;
					case 102:
						numTaps(6)
						break;
					case 103:
						numTaps(7)
						break;
					case 104:
						numTaps(8)
						break;
					case 105:
						numTaps(9)
						break;
					case 110:
						numTaps('.')
						break;
					case 190:
						numTaps('.')
						break;
					case 8:
						delNums()
						break;
				}
			}
		}
	}
}
</script>

<style scoped lang="stylus">
	::-webkit-scrollbar-thumb {
		-webkit-box-shadow: inset 0 0 6px #eee;
	}

	::-webkit-scrollbar {
		width: 4px !important;
		/*对垂直流动条有效*/
	}

	.remark {
		/deep/.ivu-input-wrapper {
			width: 91% !important;
		}

		/deep/.ivu-input-number {
			width: 91% !important;
		}

		/deep/.ivu-form-item-content {
			margin-left: 63px !important;
		}

		/deep/.ivu-form-item-label {
			width: 63px !important;
		}
	}
	
	.noCart{
		height 100%;
		.tip{
			text-align center;
			color #ccc;
		}
		.pictrue{
			width 308px;
			height 200px;
			img{
				width 100%;
				height 100%;
			}
		}
	}
	
	.goodsCard {
		max-width: 100%;
		min-width: 1100px;
	}

	.modalPay {
		/deep/.ivu-modal-body {
			padding: 0;
		}
	}

	.cash {
		/deep/.ivu-modal-body {
			padding: 0 !important;
		}
	}

	.discountCon {
		.item {
			font-size: 15px;
			margin-bottom: 10px;
		}
	}

	.cashPage {
		text-align: center;

		.right {
			width: 488px;
			background: #F5F5F5;
			padding: 16px 16px 16px 0;
			border-radius: 0 6px 6px 0;

			/deep/.ivu-btn-primary {
				width: 100px;
			}

			.rightCon {
				width: 388px;
				height: 506px;
				margin: 35px auto 20px auto;
				background-color: #fff;
				border-radius: 14px;

				.top {
					height: 80px;
					color: rgba(0, 0, 0, 0.65);
					font-size: 13px;
					padding: 0 20px;

					.num {
						font-size: 42px;
						color: rgba(0, 0, 0, 0.85);
					}
				}

				.center {
					width: 100%;
					height: 46px;
					background-color: #1890FF;
					font-size: 13px;
					color: #fff;
					padding: 0 20px;

					.num {
						font-size: 27px;
					}
				}

				.bottom {
					padding: 10px 0 0 8px;

					.item {
						width: 108px;
						height: 62px;
						background: #FAFAFA;
						border-radius: 9px;
						border: 1px solid rgba(0, 0, 0, 0.15);
						color: #1890FF;
						font-size: 32px;
						margin-left: 12px;
						margin-top: 12px;
						cursor: pointer;

						&.on {
							background: #1890FF;
							color: #FFFFFF;
							font-size: 20px;
						}

						&.spot {
							padding-bottom: 15px;
						}
					}
				}
			}
		}

		.left {
			width: 282px;
			padding: 16px 0 16px 16px;

			.pictrue {
				width: 110px;
				height: 110px;
				margin: 180px auto 0 auto;

				img {
					width: 100%;
					height: 100%;
				}
			}

			.text {
				color: rgba(0, 0, 0, 0.45);
				font-size: 14px;
				margin-top: 14px;
			}

			.money {
				color: rgba(0, 0, 0, 0.85);
				font-size: 18px;

				.num {
					font-size: 32px;
					margin-left: 5px;
				}
			}
		}
	}

	.payPage {
		text-align: center;
		padding: 16px;

		/deep/.ivu-input {
			width: 394px !important;
			text-align: center;
		}
		
		.header{
			margin:35px 0 3px 0;
		}
		
		.process{
			width 394px;
			height 158px;
			border: 1px dashed #D8D8D8;
			border-top: 1px dashed #fff;
			margin: -1px auto 0 auto; 
			&.on{
				border-top: 1px dashed #D8D8D8;
				margin-top: 20px; 
				.list{
					 padding-left 14px!important
				}
			}
			.list{
				padding 6px 10px 0 3px;
				.item{
					font-size 12px;
					color #666;
					.name{
						color #333;
						font-size 13px;
						font-weight bold;
					}
				}
			}
			.pictrue{
				width 362px;
				height 68px;
				margin 24px auto 0 auto;
				img{
					width 100%;
					height 100%;
				}
			}
		}

		.pictrue {
			width: 18px;
			height: 18px;

			img {
				width: 100%;
				height: 100%;
			}
			margin-right 7px
		}

		.text {
			color: rgba(0, 0, 0, 0.45);
			font-size: 14px;
		}

		.money {
			font-size: 18px;
			color: rgba(0, 0, 0, 0.85);

			.num {
				font-size: 32px;
				margin-left: 5px;
			}
		}

		.tip {
			width: 310px;
			height: 26px;
			background: rgba(255, 126, 0, 0.1);
			border-radius: 13px;
			font-size: 13px;
			color: #FF7E00;
			margin: 10px auto 0 auto;

			.icon {
				font-size: 16px;
				margin-right: 5px;
			}
		}

		.bnt {
			width: 394px;
			height 38px;
			margin: 28px 0 15px 0;
		}
	}

	.goods {
		flex: auto;
		margin-right: 15px;
		width: calc(100% - 540px);

		/deep/.ivu-card-body {
			padding: 10px 0 16px 0 !important;
		}

		.smCode {
			padding: 0 16px;

			/deep/.ivu-input-large {
				height: 350px !important;
				text-align: center;
				font-size: 20px !important;
			}
		}

		.goodsCon {
			flex: auto;
			padding-left: 16px;
			width: calc(100% - 100px);

			/deep/.ivu-input-group {
				.ivu-input {
					text-align: center;
				}
			}

			.input {
				padding-right: 16px;
			}

			.page {
				padding-right: 16px;
			}

			.noGood {
				margin-top: 30px;

				.pictrue {
					width: 414px;
					height: 304px;
				}

				img {
					width: 100%;
					height: 100%;
				}

				.tip {
					font-size: 15px;
					text-align: center;
					color #ccc;
				}
			}

			.list {
				overflow-x: hidden;
				overflow-y: auto;
				.item {
					width: 120px;
					height: 238px;
					background: #FAFAFA;
					margin: 16px 16px 0 0;
					position: relative;
					cursor: pointer;

					.pictrue {
						width: 100%;
						height: 152px;

						img {
							width: 100%;
							height: 100%;
						}
					}

					.text {
						padding: 8px 10px 0 10px;

						.iconfont {
							position: absolute;
							color: #1890FF;
							font-size: 20px;
							right: 5px;
							bottom: 5px;
						}

						.name {
							font-size: 16px;
							color: #000;
						}

						.stock {
							font-size: 13px;
							color: #999999;
							margin: 2px 0;
						}

						.money {
							color: #F5222D;
							font-size: 16px;
						}
					}
				}
			}
		}

		.goodClass {
			width: 100px;
			border-left: 1px solid #F0F2F5;
			cursor: pointer;
			overflow-y:auto;

			.item {
				width: 82px;
				height: 38px;
				text-align: center;
				line-height: 38px;
				font-size: 14px;
				color: #000;

				&.on {
					background-color: #1890FF;
					color: #fff;
				}
			}
		}
	}

	.conter {
		margin-right: 10px;
		margin-left: 15px;

		/deep/.ivu-card-body {
			padding: 0 !important;
		}

		.cart {
			width: 500px;

			.right {
				width: 90px;
				
				.item {
					width: 72px;
					background: #F2F3F5;
					margin: 0 auto 13px auto;
					text-align: center;
					padding: 9px 0;
					cursor: pointer;
					position: relative;
					.iconfont{
						position absolute;
						font-size 20px;
						top:-9px;
						right -7px;
						color #bbb;
					}
					
					&:nth-child(3) {
						&:hover {
							background-color: #1890FF;
							color: #fff;
						}
					}
				
					&:nth-child(4) {
						&:hover {
							background-color: #1890FF;
							color: #fff;
						}
					}
				
					&:nth-child(5) {
						&:hover {
							background-color: #1890FF;
							color: #fff;
						}
					}
				
					&.on {
						background-color: #1890FF;
						color: #fff;
					}
				}
				
				.noCart{
					.item{
						background #ccc;
						color #fff;
						cursor unset;
						
						&:nth-child(3) {
							&:hover {
								background-color: #ccc;
							}
						}
										
						&:nth-child(4) {
							&:hover {
								background-color: #ccc;
							}
						}
										
						&:nth-child(5) {
							&:hover {
								background-color: #ccc;
							}
						}
										
						&.on {
							background-color: #ccc;
						}
					}
				}
			}

			.left {
				width: 410px;
				border-right: 1px solid #eee;

				.title {
					height: 54px;
					padding-right: 17px;
					border-bottom: 1px solid #D8D8D8;
					padding-left: 16px;
				}

				.footer {
					padding: 10px 17px 15px 17px;

					.conInfo {
						font-size: 14px;
						color: #000;

						.detailed {
							color: #999999;
							font-size: 14px;
							cursor: pointer;
						}
					}

					.pay {
						margin-top: 12px;
						
						.bnt {
							width: 120px;
							height: 56px;
							border: 1px solid #1890FF;
							color: #1890FF;
							font-size: 18px;
							text-align: center;
							line-height: 56px;
							font-weight: 500;
							cursor: pointer;

							&.on {
								background: #1890FF;
								color: #fff;
							}
						}
						
						&.noCart{
							.bnt{
								border:1px solid #ccc;
								color #ccc;
								cursor unset;
								&.on{
									background: #ccc;
									color: #fff;
								}
							}
						}
					}
				}

				.truePrice {
					margin-top: 40px;
					color: #000;
					font-size: 15px;
					text-align: right;
					border-bottom: 1px solid #D8D8D8;
					padding-right: 17px;
					padding-bottom: 10px;

					.num {
						font-size: 24px;
					}

				}
			}
			
			.listCon{
				overflow-x: hidden;
				overflow-y: auto;
			}
			
			.list {
				padding-left: 16px;

				.item {
					width: 100%;
					height: 122px;
					position: relative;

					/deep/.ivu-input-number-input {
						text-align: center;
					}

					/deep/.ivu-input-number-controls-outside {
						width: 112px !important;
					}

					.pictrue {
						width: 90px;
						height: 90px;

						img {
							width: 100%;
							height: 100%;
						}
					}

					.del {
						position: absolute;
						font-size: 14px;
						color: #1890FF;
						right: 10px;
						top: 15px;
						cursor: pointer;
						padding: 2px 7px;
					}

					.cartBnt {
						position: absolute;
						right: 10px;
						height: 24px;
						bottom: 13px;

						.iconfont {
							width: 24px;
							height: 100%;
							background-color: #F2F3F5;
							text-align: center;
							line-height: 24px;
							font-size: 15px;
							color: #000;
						}

						input {
							outline: unset;
							border: 0;
							width: 60px;
							background-color: #F2F3F5;
							height: 100%;
							margin: 0 2px;
							text-align: center;
						}
					}

					.text {
						width: 286px;
						color: #000;
						font-size: 18px;
						margin-left: 10px;
						
						.end{
							color #999;
							font-size 13px;
						}

						.name {
							font-size: 16px;
							margin-top: 5px;
							width: 230px;
						}

						.info {
							color: #999999;
							font-size: 12px;
							margin: 10px 0 14px 0;
							cursor: pointer;

							.iconfont {
								font-size: 12px;
								margin-left: 5px;
							}
						}
						
						&.invalid{
							.info{
								cursor unset;
							}
							.name{
								color #999;
							}
						}
					}
				}
			}
		}

		.title {
			color: rgba(0, 0, 0, 0.85);

			.text {
				font-size: 16px;
				font-weight: 500;
			}

			.pictrue {
				width: 32px;
				height: 32px;
				border-radius: 50%;
				cursor: pointer;

				img {
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}
			}

			.info {
				font-size: 14px;
				margin-left: 8px;
				cursor: pointer;

				.iconfont {
					font-size: 12px;
					margin-left: 5px;
				}

				&:hover {
					color: #2d8cf0;
				}
			}
		}
	}

	.header {
		.title {
			font-size: 18px;
			font-weight: 500;
		}

		.right {
			.pictrue {
				width: 32px;
				height: 32px;
				border-radius: 50%;

				img {
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}
			}

			.info {
				font-size: 14px;
				font-weight: 400;
				color: rgba(0, 0, 0, 0.85);

				span {
					padding: 0 8px;

					&~span {
						border-left: 1px solid #DDDDDD;
					}
				}
			}

			.bnt {
				margin-left: 20px;
			}
		}
	}
</style>
