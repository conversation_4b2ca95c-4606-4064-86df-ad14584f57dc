<template>
  <div>
    <Modal
      v-model="isTemplate"
      scrollable
      footer-hide
      closable
      :title="title"
      :z-index="1"
      width="700"
      @on-cancel="cancel"
    >
      <div class="article-manager">
        <Card :bordered="false" dis-hover class="ivu-mt">
          <Form
            ref="formItem"
            :model="formItem"
            :label-width="labelWidth"
            :label-position="labelPosition"
            :rules="ruleValidate"
            @submit.native.prevent
          >
            <Row type="flex" :gutter="24">
              <Col span="24">
                <FormItem label="店铺：" prop="shop_id">
                  <div class="acea-row">
                    <Select v-model="formItem.shop_id" clearable v-width="'50%'" class="mr20">
                      <Option
                        v-for="(item, index) in shopList"
                        :value="item.id"
                        :key="index"
                      >{{ item.name }}</Option>
                    </Select>
                  </div>
                </FormItem>
              </Col>
              <Col span="24">
                <Col v-bind="grid">
                  <FormItem label="分类名称：" prop="name" label-for="name">
                    <Input
                      v-model="formItem.name"
                      maxlength="20"
                      show-word-limit
                      placeholder="请输入分类名称"
                    />
                  </FormItem>
                </Col>
              </Col>
              <Col span="24">
                <Col v-bind="grid">
                  <FormItem label="分类图片 ：" prop="img">
                    <div class="picBox" @click="modalPicTap()">
                      <div class="pictrue" v-if="formItem.img">
                        <img v-lazy="formItem.img" />
                      </div>
                      <div class="upLoad" v-else>
                        <div class="iconfont">+</div>
                      </div>
                    </div>
                  </FormItem>
                </Col>
              </Col>
              <Col v-bind="grid">
                <FormItem label="排序：">
                  <InputNumber
                    :min="0"
                    v-width="'90%'"
                    v-model="formItem.sort"
                    placeholder="请输入排序"
                  />
                </FormItem>
              </Col>
              <Col span="24">
                <Col v-bind="grid">
                  <FormItem label="状态：" label-for="is_show" prop="is_show">
                    <Switch
                      size="large"
                      v-model="formItem.is_show"
                      :false-value="1"
                      :true-value="0"
                    >
                      <span slot="open" :true-value="0">显示</span>
                      <span slot="close" :false-value="1">隐藏</span>
                    </Switch>
                  </FormItem>
                </Col>
              </Col>
            </Row>
            <Row style="justify-content: space-around;">
              <Col>
                <Button
                  type="primary"
                  class="btn"
                  @click="handleSubmit('formItem')"
                >{{ formItem.id != 0 ? '修改' : '添加' }}</Button>
              </Col>
            </Row>
            <Spin size="large" fix v-if="spinShow"></Spin>
          </Form>
        </Card>

        <Modal
          v-model="modalPic"
          width="950px"
          scrollable
          footer-hide
          closable
          title="上传店铺图片"
          :mask-closable="false"
          :z-index="1"
        >
          <uploadPictures
            isChoice="单选"
            @getPic="getPic"
            :gridBtn="gridBtn"
            :gridPic="gridPic"
            v-if="modalPic"
          ></uploadPictures>
        </Modal>
      </div>
    </Modal>
  </div>
</template>

<script>
import { shopListApi, categoryUpdateApi, categoryGetInfoApi } from '@/api/coffee';
import { mapState } from 'vuex';
import uploadPictures from '@/components/uploadPictures';
export default {
  name: 'category',
  components: { uploadPictures },
  props: {},
  data() {
    return {
      // 模板状态标识
      isTemplate: false,
      // 弹窗Title
      title: '',
      // 店铺列表
      shopList: [],
      // 表单数据
      formItem: {
        id: 0,
        // 店铺
        shop_id: '',
        // 店铺名称
        name: '',
        // 小图标
        img: [],
        // 店铺状态
        is_show: 0,
        // 排序
        sort: 100,
      },
      // 数据加载loading
      spinShow: false,
      // 验证规则
      ruleValidate: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ]
      },
      // 弹窗样式
      grid: {
        xl: 20,
        lg: 20,
        md: 20,
        sm: 24,
        xs: 24
      },
      gridPic: {
        xl: 6,
        lg: 8,
        md: 12,
        sm: 12,
        xs: 12
      },
      gridBtn: {
        xl: 4,
        lg: 8,
        md: 8,
        sm: 8,
        xs: 8
      },
      // 图片上传标识
      modalPic: false,
      // 修改 与 删除 标识
      add: 0
    }
  },
  computed: {
    ...mapState('admin/layout', [
      'isMobile'
    ]),
    labelWidth() {
      return this.isMobile ? undefined : 120;
    },
    labelPosition() {
      return this.isMobile ? 'top' : 'right';
    }
  },
  methods: {
    clearFrom() {
      this.formItem = {
        id: 0,
        // 店铺
        shop_id: '',
        // 店铺名称
        name: '',
        // 小图标
        img: [],
        // 店铺状态
        is_show: 0,
        // 排序
        sort: 100,
      }
    },
    // 结束弹窗并清除表单数据
    cancel() {
      this.$refs['formItem'].resetFields();
      this.clearFrom();
    },
    // 获取店铺
    getShop() {
      this.spinShow = true;
      shopListApi().then(res => {
        this.spinShow = false;
        this.shopList = res.data.list
      })
    },
    // 详情
    getInfo(id) {
      let that = this;
      that.formItem.id = id;
      that.spinShow = true;
      categoryGetInfoApi(id).then(res => {
        this.formItem = res.data;
        that.spinShow = false;
      }).catch(function (res) {
        that.spinShow = false;
        that.$Message.error(res.msg);
      })
    },
    // 点击商品图
    modalPicTap() {
      this.modalPic = true;
    },
    // 选中图片
    getPic(pc) {
      this.formItem.img = pc.att_dir;
      this.modalPic = false;
    },
    // 提交
    handleSubmit(name) {
      let params = this.formItem
      params.id = this.formItem.id
      categoryUpdateApi(params).then(async res => {
        this.$Message.success(res.msg);
        this.isTemplate = false;
        this.$parent.getList();
        this.$refs[name].resetFields();
        this.clearFrom();
      }).catch(res => {
        this.$Message.error(res.msg);
      })
    }
  }
}
</script>

<style scoped lang="stylus">
  .pictrue {
    width: 60px;
    height: 60px;
    border: 1px dotted rgba(0, 0, 0, 0.1);
    margin-right: 15px;
    margin-bottom 10px;
    display: inline-block;
    position: relative;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }

    .btndel {
      position: absolute;
      z-index: 1;
      width: 20px !important;
      height: 20px !important;
      left: 46px;
      top: -4px;
    }
  }
  .upLoad {
    width: 58px;
    height: 58px;
    line-height: 58px;
    border: 1px dotted rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.02);
    cursor: pointer;
  }
	.map-sty {
		width: 90%;
		text-align: right;
		margin: 0 0 0 10%;
	}
	/deep/.ivu-card-body{
		padding 16px 0 0 0!important;
	}
	.footer{
		width 100%;
		height 50px;
		box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.05);
		margin-top 50px;
	}
	/deep/.ivu-btn-primary{
		width 86px;
	}
	.btn{
		margin-top: 20px;
	}
	.inputW{
		width 400px;
	}
	.ivu-mt{
		min-width 580px;
	}
	.picBox
		display: inline-block;
		cursor: pointer;
		.upLoad
			width: 58px;
			height: 58px;
			line-height: 58px;
			border: 1px dotted rgba(0, 0, 0, 0.1);
			border-radius: 4px;
			background: rgba(0, 0, 0, 0.02);
		.pictrue
			width: 60px;
			height: 60px;
			border: 1px dotted rgba(0, 0, 0, 0.1);
			margin-right: 10px;
			img
				width: 100%;
				height: 100%;
		.iconfont
			color: #CCCCCC;
			font-size 26px;
			text-align center
			
</style>
