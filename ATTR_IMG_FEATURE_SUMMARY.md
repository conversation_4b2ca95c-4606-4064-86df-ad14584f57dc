# 规格值图片上传功能实现总结

## 功能描述
为商品规格值增加图片上传功能，每个规格值现在可以上传一张图片，字段名为 `attr_img`。

## 主要修改

### 1. 数据结构变更
- **原格式**: 规格值为字符串数组，如 `["小杯(¥5)", "大杯(¥8)"]`
- **新格式**: 规格值为对象数组，如：
```javascript
[
  {
    name: "小杯",
    price: "5",
    attr_img: "图片URL"
  },
  {
    name: "大杯", 
    price: "8",
    attr_img: "图片URL"
  }
]
```

### 2. 界面修改
- 在每个规格值标签旁边添加了小型图片上传按钮
- 图片上传按钮尺寸为 30x30px，适合紧凑布局
- 支持点击上传和预览已上传的图片

### 3. 兼容性处理
- 自动转换旧的字符串格式为新的对象格式
- 在加载规格模板时进行格式转换
- 在编辑商品时加载数据后进行格式转换
- 确保新旧数据格式都能正常工作

### 4. 核心方法修改

#### `createAttrName()` - 创建新规格
- 修改为创建对象格式的规格值
- 包含 name、price、attr_img 三个字段

#### `createAttr()` - 添加规格值
- 修改为创建对象格式的规格值
- 改进去重逻辑，基于 name 字段去重

#### `modalAttrPicTap()` - 规格值图片上传
- 新增方法，处理规格值图片上传点击事件
- 记录当前操作的规格索引和规格值索引

#### `getPic()` - 图片选择回调
- 扩展原有方法，支持规格值图片设置
- 自动处理字符串格式到对象格式的转换

#### `confirm()` - 规格模板应用
- 添加格式转换逻辑
- 确保模板数据转换为新的对象格式

### 5. 样式调整
- 添加 `.attr-item-wrapper` 样式，用于规格值项布局
- 添加 `.attr-img-upload` 样式，用于图片上传按钮定位
- 添加 `.attr-img` 样式，设置规格值图片尺寸
- 调整 `.rulesBox` 布局为垂直排列

## 使用说明

1. **添加新规格值**: 在规格值输入框中输入名称和价格，点击添加后，会自动创建包含图片字段的规格值
2. **上传规格值图片**: 点击规格值标签旁边的相机图标，选择图片上传
3. **编辑现有商品**: 系统会自动转换旧格式数据，无需手动处理
4. **使用规格模板**: 应用模板时会自动转换为新格式

## 技术特点

- **向后兼容**: 完全兼容现有的字符串格式数据
- **自动转换**: 无缝转换旧数据到新格式
- **用户友好**: 界面简洁，操作直观
- **数据完整**: 保留所有原有功能，仅增加图片字段

## 注意事项

- 规格值图片为可选字段，不上传图片不影响正常使用
- 系统会自动处理数据格式转换，开发者无需担心兼容性问题
- 图片上传复用现有的图片上传组件和逻辑
