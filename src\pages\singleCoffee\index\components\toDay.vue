<template>
  <div>
    <Row type="flex" align="middle" :gutter="10" class="ivu-mt">
      <Col
        v-for="(item, index) in cateLists"
        :lg="4"
        :md="12"
        :sm="24"
        :xs="24"
        class="card_cent"
        :key="index"
      >
        <Card class="cart_name">
          <div @click="cate(item.id, item.name)">
            {{ item.name }}
          </div>
        </Card>
      </Col>
    </Row>
    <Form ref="formValidate"
    :label-width="labelWidth"
    :label-position="labelPosition"
     @submit.native.prevent>
      <Row type="flex" :gutter="24">
        <Col v-bind="grid">
          <FormItem label="商品选择：">
            <Select
              v-model="item"
              placeholder="请选择"
              clearable
              @on-change="userSearchs"
            >
              <Option
                v-for="(item, index) in productLists"
                :value="index"
                :key="index"
                >{{ item.name }}</Option
              >
            </Select>
          </FormItem>
        </Col>
      </Row>
    </Form>
  </div>
</template>

<script>
import { bottom_trade_coffee_v1 } from "@/api/statistic";
import echartsNew from "@/components/echartsNew/index";
import { productListApi } from '@/api/coffee';
import { mapMutations } from "vuex";
export default {
  name: "ToDay",
  components: {
    echartsNew,
  },
  data() {
    return {
      grid: {
        xl: 8,
        lg: 8,
        md: 8,
        sm: 24,
        xs: 24,
      },
      category_id: "",
      cardLists: [],
      cateLists: [],
      productLists: [],
      item: "",
    };
  },
  beforeDestroy() {
    if (this.visitChart) {
      this.visitChart.dispose();
      this.visitChart = null;
    }
  },
  computed: {
    labelWidth() {
      return this.isMobile ? undefined : 75;
    },
    labelPosition() {
      return this.isMobile ? "top" : "right";
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    ...mapMutations("admin/coffee", [
      "setShopName",
      "setCateName",
      "setProductName",
      "setCateId",
      "setShopId",
      "setProductId",
    ]),
    getList(id, name) {
      this.listLoading = true;
      bottom_trade_coffee_v1({ shopId: this.$store.state.admin.user.info.shop_id })
        .then((res) => {
          this.item = "";
          this.productLists = [];
          if (!id) {
            this.cardLists = res.data.shop;
            this.setShopId("");
            this.setShopName("汇总");

            this.setCateId("");
            this.setCateName("");

            this.setProductName("");
            this.setProductId("");
          } else {
            this.setCateName("");
            this.setShopName(name);
            this.setShopId(id);

            this.setCateId("");
            this.setCateName("");

            this.setProductName("");
            this.setProductId("");

            this.$parent.$refs.tran.getStatistics();
          }
          this.cateLists = res.data.category;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$Message.error(res.msg);
        });
    },
    userSearchs() {
      if(this.item === undefined) {
        this.setProductId("");
        this.setProductName("");
      } else {
        let productId = this.productLists[this.item].id;
        let productName = '-' + this.productLists[this.item].name;
        this.setProductId(productId);
        this.setProductName(productName);
      }
      this.$parent.$refs.tran.getStatistics();
    },
    cate(id, name) {
      name = '-' + name
      this.setCateName(name);
      this.setCateId(id);
      this.item = "";
      // 查询商品
			productListApi({
				category_id: id,
				type: 'all',
				page: 1,
				limit: 99,
			}).then(res => {
				this.productLists = res.data.list
				this.loading = false
			})
      this.$parent.$refs.tran.getStatistics();
    },
  },
};
</script>
<!-- lang="stylus" -->
<style scoped lang="stylus">

.card_box_cir1 >>> .ivu-icon
   font-size: 26px
   color: #fff
 .one
    background #E4ECFF
.two
    background #FFF3E0
.three
    background #EAF9E1
.four
    background #FFEAF4
.five
    background #F1E4FF
.one1
    background #4D7CFE
.two1
    background #FFAB2B
.three1
    background #6DD230
.four1
    background #FF85C0
.five1
    background #B37FEB
.card_box
    width 100%
    height 100%
    display flex
    cursor: pointer
    align-items: center
    /*justify-content: center*/
    padding: 25px
    box-sizing: border-box
    border-radius: 4px
    .card_box_cir
        width 60px
        height 60px
        border-radius: 50%
        overflow: hidden
        margin-right: 20px
        display: flex
        justify-content: center
        align-items: center
        .card_box_cir1
            width 48px
            height 48px
            border-radius: 50%
            display: flex
            justify-content: center
            align-items: center
    .card_box_txt
        .sp1
            display block
            color #252631
            font-size 24px
        .sp2
            display block
            color #98A9BC
            font-size 24px
        .sp3
            font-size 12px
.card_box_cir1 .iconfont{
            color #fff;
            font-size 20px;

}
.cart_name {
    margin-bottom: 10px;
    cursor: pointer;
}
</style>
