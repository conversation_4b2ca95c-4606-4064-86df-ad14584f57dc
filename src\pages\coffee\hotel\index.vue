<template>
    <div>
      <div class="i-layout-page-header">
        <PageHeader title="轻食管理" hidden-breadcrumb></PageHeader>
      </div>
      <Card :bordered="false" dis-hover class="ivu-mt">
        <Form
          ref="formValidate"
          :model="formValidate"
          :label-width="labelWidth"
          :label-position="labelPosition"
          class="tabform"
          @submit.native.prevent
        >
          <Row :gutter="24" type="flex">
            <Col :xl="6" :lg="6" :md="6" :sm="12" :xs="12">
              <FormItem label="店铺：" label-for="shop_id">
                  <Select
                      v-model="formValidate.shop_id"
                      placeholder="请选择"
                      clearable
                      @on-change="userSearchs"
                  >
                  <Option
                      v-for="(item, index) in shopList"
                      :value="item.id"
                      :key="index"
                  >{{ item.name }}
                  </Option>
                  </Select>
              </FormItem>
          </Col>
            <Col :xl="6" :lg="6" :md="12" :sm="24" :xs="24">
              <FormItem label="手机号：">
                <Input
                  enter-button
                  placeholder="请输入"
                  element-id="name"
                  v-model="formValidate.phone"
                />
              </FormItem>
            </Col>
            <Col :xl="6" :lg="6" :md="12" :sm="24" :xs="24">
              <FormItem label="时间范围：" class="tab_data">
                <DatePicker
                  :editable="false"
                  style="width: 80%"
                  @on-change="onchangeTime"
                  format="yyyy/MM/dd"
                  type="daterange"
                  placement="bottom-end"
                  placeholder="自定义时间"
                ></DatePicker>
              </FormItem>
            </Col>
            <Col span="24">
              <FormItem>
                <Button type="primary" icon="ios-search" @click="userSearchs"
                  >搜索</Button
                >
              </FormItem>
            </Col>
          </Row>
        </Form>
      </Card>
      <cards-data :cardLists="cardLists"></cards-data>
      <transaction-from ref="tran"></transaction-from>
      <Card :bordered="false" dis-hover>
        <Table
          ref="table"
          highlight-row
          :columns="columns"
          :data="tabList"
          :loading="loading"
          no-data-text="暂无数据"
          no-filtered-data-text="暂无筛选结果"
        >
          <template slot-scope="{ row }" slot="order">
            商品订单号： {{row.ordernum }}<br/>
            商品取餐号： {{row.take_num }}
          </template>
          <template slot-scope="{ row, index }" slot="action">
            <a @click="print(row)">打印</a>
        </template>
        </Table>
        <div class="acea-row row-right page">
          <Page
            :total="total"
            :current="formValidate.page"
            show-elevator
            show-total
            :page-size="formValidate.limit"
            @on-change="pageChange"
          />
        </div>
      </Card>
    </div>
  </template>
  
  <script>
  import cardsData from "@/components/cards/cards";
  import { mapState } from "vuex";
  import {  hotelListApi, hotelPrintApi, shopListApi} from "@/api/coffee";
  import transactionFrom from "./components/transaction";
  import { mapMutations } from "vuex";
  export default {
    name: "coffee_bill",
    components: { cardsData,transactionFrom },
    data() {
      return {
        // 店铺列表
        shopList: [],
        cardLists: [],
        formValidate: {
          shop_id:'',
          phone: "",
          start_time: "",
          end_time: "",
          page: 1, // 当前页
          limit: 10, // 每页显示条数
        },
        loading: false,
        tabList: [],
        total: 0,
        columns: [
          {
            title: "门店",
            key: "shop_name",
            minwidth: 100,
          },
          {
            title: "下单号",
            key: "order_num",
            minwidth: 40,
          },
          {
            title: "手机号",
            key: "phone",
            minwidth: 100,
          },
          {
            title: "商品名称",
            key: "coffee_name",
            minwidth: 80,
          }, 
          {
            title: "关联订单信息",
            slot: "order",
            minwidth: 80,
          },
          {
            title: "支付金额",
            key: "pay_price",
            minwidth: 60,
          },
          {
            title: "销量",
            key: "num",
            minwidth: 50,
          },
          {
            title: "单价",
            key: "price",
            minwidth: 50,
          },
          {
            title: "备注",
            key: "remark",
            minwidth: 80,
          },
          {
            title: "创建时间",
            key: "create_time",
            sortable: true,
            minwidth: 80,
          },
          {
              title: '操作',
              slot: 'action',
              fixed: 'right',
              minWidth: 80,
              align: 'center'
          }
        ],
      };
    },
    computed: {
      ...mapState("admin/layout", ["isMobile"]),
      labelWidth() {
        return this.isMobile ? undefined : 80;
      },
      labelPosition() {
        return this.isMobile ? "top" : "right";
      },
    },
    created() {

      shopListApi().then(res => {
            this.shopList = res.data.list
            this.setShopId(res.data.list[0].id)
            this.formValidate.shop_id = res.data.list[0].id
            this.getList();
            this.$refs.tran.getStatistics();
       })
    },
    methods: {
      ...mapMutations("admin/coffee", [
      "setShopId",
      ]),
      // 时间
      onchangeTime(e) {
        this.formValidate.start_time = e[0];
        this.formValidate.end_time = e[1];
      },
      print(item) {
        hotelPrintApi({id:item.ordernum})
          .then(async (res) => {
            console.log(res)
        }).catch((res) => {
          this.loading = false;
          this.$Message.error(res.msg);
        });
      },
      // 列表
      getList() {
        this.loading = true;
        hotelListApi(this.formValidate)
          .then(async (res) => {
            let data = res.data;
            this.tabList = data.list;
            this.total = data.count;
            this.loading = false;
            this.cardLists = data.card
          })
          .catch((res) => {
            this.loading = false;
            this.$Message.error(res.msg);
          });
      },
      pageChange(index) {
        this.formValidate.page = index;
        this.getList();
      },
      // 搜索
      userSearchs() {
        if(this.formValidate.shop_id) {
          this.setShopId(this.formValidate.shop_id);
        } else {
          this.setShopId("");
        }
        this.formValidate.page = 1;
        this.getList();
        this.$refs.tran.getStatistics();
      }
    },
  };
  </script>
  
  <style scoped lang="stylus">
  .ivu-form-label-left >>> .ivu-form-item-label {
    text-align: right;
  }
  
  .tabform .export {
    margin-left: 10px;
  }
  
  .red {
    color: #FF5722;
  }
  
  .green {
    color: #009688;
  }
  </style>
  