// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import request from "@/plugins/request";

/**
 * @description 服务 -- 理发
 * @param {Object} param params {Object} 传值参数
 */
export function hairService(params) {
    return request({
        url: "hair_service",
        method: "get",
        params,
    });
}

/**
 * @description 服务 -- 理发
 * @param {Object} param params {Object} 传值参数
 */
export function hairDetail(params) {
    return request({
        url: "hair_service/detail",
        method: "get",
        params,
    });
}

/**
 * @description 服务 -- 理发
 * @param {Object} param params {Object} 传值参数
 */
export function hairAdd(params) {
    return request({
        url: "hair_service/add",
        method: "post",
        params,
    });
}

/**
 * @description 服务 -- 理发
 * @param {Object} param params {Object} 传值参数
 */
export function hairEdit(params) {
    return request({
        url: "hair_service/edit",
        method: "post",
        params,
    });
}

/**
 * @description 服务 -- 理发
 * @param {Object} param params {Object} 传值参数
 */
export function hairDel(params) {
    return request({
        url: "hair_service/del",
        method: "post",
        params,
    });
}
