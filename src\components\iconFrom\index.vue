<template>
    <div class="iconBox">
        <Input v-model="iconVal" placeholder="输入关键词搜索,注意全是英文" clearable style="width: 300px"
               @on-change="upIcon(iconVal)" ref="search"/>
        <div class="trees-coadd">
            <div class="scollhide">
                <div class="trees">
                    <ul class="list-inline">
                        <li class="icons-item" v-for="(item,i) in list" :key="i">
                            <Icon :type="item.type" @click="iconChange(item.type)" class="ivu-icon"/>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'index',
        data () {
            return {
                iconVal: '',
                modals2: false,
                list: [],
                search: [
                    { 'type': 'ios-add' },
                    { 'type': 'md-add' },
                    { 'type': 'ios-add-circle' },
                    { 'type': 'ios-add-circle-outline' },
                    { 'type': 'md-add-circle' },
                    { 'type': 'ios-alarm' },
                    { 'type': 'ios-alarm-outline' },
                    { 'type': 'md-alarm' },
                    { 'type': 'ios-albums' },
                    { 'type': 'ios-albums-outline' },
                    { 'type': 'md-albums' },
                    { 'type': 'ios-alert' },
                    { 'type': 'ios-alert-outline' },
                    { 'type': 'md-alert' },
                    { 'type': 'ios-american-football' },
                    { 'type': 'ios-american-football-outline' },
                    { 'type': 'md-american-football' },
                    { 'type': 'ios-analytics' },
                    { 'type': 'ios-analytics-outline' },
                    { 'type': 'md-analytics' },
                    { 'type': 'logo-android' },
                    { 'type': 'logo-angular' },
                    { 'type': 'ios-aperture' },
                    { 'type': 'ios-aperture-outline' },
                    { 'type': 'md-aperture' },
                    { 'type': 'logo-apple' },
                    { 'type': 'ios-apps' },
                    { 'type': 'ios-apps-outline' },
                    { 'type': 'md-apps' },
                    { 'type': 'ios-appstore' },
                    { 'type': 'ios-appstore-outline' },
                    { 'type': 'md-appstore' },
                    { 'type': 'ios-archive' },
                    { 'type': 'ios-archive-outline' },
                    { 'type': 'md-archive' },
                    { 'type': 'ios-arrow-back' },
                    { 'type': 'md-arrow-back' },
                    { 'type': 'ios-arrow-down' },
                    { 'type': 'md-arrow-down' },
                    { 'type': 'ios-arrow-dropdown' },
                    { 'type': 'md-arrow-dropdown' },
                    { 'type': 'ios-arrow-dropdown-circle' },
                    { 'type': 'md-arrow-dropdown-circle' },
                    { 'type': 'ios-arrow-dropleft' },
                    { 'type': 'md-arrow-dropleft' },
                    { 'type': 'ios-arrow-dropleft-circle' },
                    { 'type': 'md-arrow-dropleft-circle' },
                    { 'type': 'ios-arrow-dropright' },
                    { 'type': 'md-arrow-dropright' },
                    { 'type': 'ios-arrow-dropright-circle' },
                    { 'type': 'md-arrow-dropright-circle' },
                    { 'type': 'ios-arrow-dropup' },
                    { 'type': 'md-arrow-dropup' },
                    { 'type': 'ios-arrow-dropup-circle' },
                    { 'type': 'md-arrow-dropup-circle' },
                    { 'type': 'ios-arrow-forward' },
                    { 'type': 'md-arrow-forward' },
                    { 'type': 'ios-arrow-round-back' },
                    { 'type': 'md-arrow-round-back' },
                    { 'type': 'ios-arrow-round-down' },
                    { 'type': 'md-arrow-round-down' },
                    { 'type': 'ios-arrow-round-forward' },
                    { 'type': 'md-arrow-round-forward' },
                    { 'type': 'ios-arrow-round-up' },
                    { 'type': 'md-arrow-round-up' },
                    { 'type': 'ios-arrow-up' },
                    { 'type': 'md-arrow-up' },
                    { 'type': 'ios-at' },
                    { 'type': 'ios-at-outline' },
                    { 'type': 'md-at' },
                    { 'type': 'ios-attach' },
                    { 'type': 'md-attach' },
                    { 'type': 'ios-backspace' },
                    { 'type': 'ios-backspace-outline' },
                    { 'type': 'md-backspace' },
                    { 'type': 'ios-barcode' },
                    { 'type': 'ios-barcode-outline' },
                    { 'type': 'md-barcode' },
                    { 'type': 'ios-baseball' },
                    { 'type': 'ios-baseball-outline' },
                    { 'type': 'md-baseball' },
                    { 'type': 'ios-basket' },
                    { 'type': 'ios-basket-outline' },
                    { 'type': 'md-basket' },
                    { 'type': 'ios-basketball' },
                    { 'type': 'ios-basketball-outline' },
                    { 'type': 'md-basketball' },
                    { 'type': 'ios-battery-charging' },
                    { 'type': 'md-battery-charging' },
                    { 'type': 'ios-battery-dead' },
                    { 'type': 'md-battery-dead' },
                    { 'type': 'ios-battery-full' },
                    { 'type': 'md-battery-full' },
                    { 'type': 'ios-beaker' },
                    { 'type': 'ios-beaker-outline' },
                    { 'type': 'md-beaker' },
                    { 'type': 'ios-beer' },
                    { 'type': 'ios-beer-outline' },
                    { 'type': 'md-beer' },
                    { 'type': 'ios-bicycle' },
                    { 'type': 'md-bicycle' },
                    { 'type': 'logo-bitcoin' },
                    { 'type': 'ios-bluetooth' },
                    { 'type': 'md-bluetooth' },
                    { 'type': 'ios-boat' },
                    { 'type': 'ios-boat-outline' },
                    { 'type': 'md-boat' },
                    { 'type': 'ios-body' },
                    { 'type': 'ios-body-outline' },
                    { 'type': 'md-body' },
                    { 'type': 'ios-bonfire' },
                    { 'type': 'ios-bonfire-outline' },
                    { 'type': 'md-bonfire' },
                    { 'type': 'ios-book' },
                    { 'type': 'ios-book-outline' },
                    { 'type': 'md-book' },
                    { 'type': 'ios-bookmark' },
                    { 'type': 'ios-bookmark-outline' },
                    { 'type': 'md-bookmark' },
                    { 'type': 'ios-bookmarks' },
                    { 'type': 'ios-bookmarks-outline' },
                    { 'type': 'md-bookmarks' },
                    { 'type': 'ios-bowtie' },
                    { 'type': 'ios-bowtie-outline' },
                    { 'type': 'md-bowtie' },
                    { 'type': 'ios-briefcase' },
                    { 'type': 'ios-briefcase-outline' },
                    { 'type': 'md-briefcase' },
                    { 'type': 'ios-browsers' },
                    { 'type': 'ios-browsers-outline' },
                    { 'type': 'md-browsers' },
                    { 'type': 'ios-brush' },
                    { 'type': 'ios-brush-outline' },
                    { 'type': 'md-brush' },
                    { 'type': 'logo-buffer' },
                    { 'type': 'ios-bug' },
                    { 'type': 'ios-bug-outline' },
                    { 'type': 'md-bug' },
                    { 'type': 'ios-build' },
                    { 'type': 'ios-build-outline' },
                    { 'type': 'md-build' },
                    { 'type': 'ios-bulb' },
                    { 'type': 'ios-bulb-outline' },
                    { 'type': 'md-bulb' },
                    { 'type': 'ios-bus' },
                    { 'type': 'ios-bus-outline' },
                    { 'type': 'md-bus' },
                    { 'type': 'ios-cafe' },
                    { 'type': 'ios-cafe-outline' },
                    { 'type': 'md-cafe' },
                    { 'type': 'ios-calculator' },
                    { 'type': 'ios-calculator-outline' },
                    { 'type': 'md-calculator' },
                    { 'type': 'ios-calendar' },
                    { 'type': 'ios-calendar-outline' },
                    { 'type': 'md-calendar' },
                    { 'type': 'ios-call' },
                    { 'type': 'ios-call-outline' },
                    { 'type': 'md-call' },
                    { 'type': 'ios-camera' },
                    { 'type': 'ios-camera-outline' },
                    { 'type': 'md-camera' },
                    { 'type': 'ios-car' },
                    { 'type': 'ios-car-outline' },
                    { 'type': 'md-car' },
                    { 'type': 'ios-card' },
                    { 'type': 'ios-card-outline' },
                    { 'type': 'md-card' },
                    { 'type': 'ios-cart' },
                    { 'type': 'ios-cart-outline' },
                    { 'type': 'md-cart' },
                    { 'type': 'ios-cash' },
                    { 'type': 'ios-cash-outline' },
                    { 'type': 'md-cash' },
                    { 'type': 'ios-chatboxes' },
                    { 'type': 'ios-chatboxes-outline' },
                    { 'type': 'md-chatboxes' },
                    { 'type': 'ios-chatbubbles' },
                    { 'type': 'ios-chatbubbles-outline' },
                    { 'type': 'md-chatbubbles' },
                    { 'type': 'ios-checkbox' },
                    { 'type': 'ios-checkbox-outline' },
                    { 'type': 'md-checkbox' },
                    { 'type': 'md-checkbox-outline' },
                    { 'type': 'ios-checkmark' },
                    { 'type': 'md-checkmark' },
                    { 'type': 'ios-checkmark-circle' },
                    { 'type': 'ios-checkmark-circle-outline' },
                    { 'type': 'md-checkmark-circle' },
                    { 'type': 'md-checkmark-circle-outline' },
                    { 'type': 'logo-chrome' },
                    { 'type': 'ios-clipboard' },
                    { 'type': 'ios-clipboard-outline' },
                    { 'type': 'md-clipboard' },
                    { 'type': 'ios-clock' },
                    { 'type': 'ios-clock-outline' },
                    { 'type': 'md-clock' },
                    { 'type': 'ios-close' },
                    { 'type': 'md-close' },
                    { 'type': 'ios-close-circle' },
                    { 'type': 'ios-close-circle-outline' },
                    { 'type': 'md-close-circle' },
                    { 'type': 'ios-closed-captioning' },
                    { 'type': 'ios-closed-captioning-outline' },
                    { 'type': 'md-closed-captioning' },
                    { 'type': 'ios-cloud' },
                    { 'type': 'ios-cloud-outline' },
                    { 'type': 'md-cloud' },
                    { 'type': 'ios-cloud-circle' },
                    { 'type': 'ios-cloud-circle-outline' },
                    { 'type': 'md-cloud-circle' },
                    { 'type': 'ios-cloud-done' },
                    { 'type': 'ios-cloud-done-outline' },
                    { 'type': 'md-cloud-done' },
                    { 'type': 'ios-cloud-download' },
                    { 'type': 'ios-cloud-download-outline' },
                    { 'type': 'md-cloud-download' },
                    { 'type': 'md-cloud-outline' },
                    { 'type': 'ios-cloud-upload' },
                    { 'type': 'ios-cloud-upload-outline' },
                    { 'type': 'md-cloud-upload' },
                    { 'type': 'ios-cloudy' },
                    { 'type': 'ios-cloudy-outline' },
                    { 'type': 'md-cloudy' },
                    { 'type': 'ios-cloudy-night' },
                    { 'type': 'ios-cloudy-night-outline' },
                    { 'type': 'md-cloudy-night' },
                    { 'type': 'ios-code' },
                    { 'type': 'md-code' },
                    { 'type': 'ios-code-download' },
                    { 'type': 'md-code-download' },
                    { 'type': 'ios-code-working' },
                    { 'type': 'md-code-working' },
                    { 'type': 'logo-codepen' },
                    { 'type': 'ios-cog' },
                    { 'type': 'ios-cog-outline' },
                    { 'type': 'md-cog' },
                    { 'type': 'ios-color-fill' },
                    { 'type': 'ios-color-fill-outline' },
                    { 'type': 'md-color-fill' },
                    { 'type': 'ios-color-filter' },
                    { 'type': 'ios-color-filter-outline' },
                    { 'type': 'md-color-filter' },
                    { 'type': 'ios-color-palette' },
                    { 'type': 'ios-color-palette-outline' },
                    { 'type': 'md-color-palette' },
                    { 'type': 'ios-color-wand' },
                    { 'type': 'ios-color-wand-outline' },
                    { 'type': 'md-color-wand' },
                    { 'type': 'ios-compass' },
                    { 'type': 'ios-compass-outline' },
                    { 'type': 'md-compass' },
                    { 'type': 'ios-construct' },
                    { 'type': 'ios-construct-outline' },
                    { 'type': 'md-construct' },
                    { 'type': 'ios-contact' },
                    { 'type': 'ios-contact-outline' },
                    { 'type': 'md-contact' },
                    { 'type': 'ios-contacts' },
                    { 'type': 'ios-contacts-outline' },
                    { 'type': 'md-contacts' },
                    { 'type': 'ios-contract' },
                    { 'type': 'md-contract' },
                    { 'type': 'ios-contrast' },
                    { 'type': 'md-contrast' },
                    { 'type': 'ios-copy' },
                    { 'type': 'ios-copy-outline' },
                    { 'type': 'md-copy' },
                    { 'type': 'ios-create' },
                    { 'type': 'ios-create-outline' },
                    { 'type': 'md-create' },
                    { 'type': 'ios-crop' },
                    { 'type': 'ios-crop-outline' },
                    { 'type': 'md-crop' },
                    { 'type': 'logo-css3' },
                    { 'type': 'ios-cube' },
                    { 'type': 'ios-cube-outline' },
                    { 'type': 'md-cube' },
                    { 'type': 'ios-cut' },
                    { 'type': 'ios-cut-outline' },
                    { 'type': 'md-cut' },
                    { 'type': 'logo-designernews' },
                    { 'type': 'ios-desktop' },
                    { 'type': 'ios-desktop-outline' },
                    { 'type': 'md-desktop' },
                    { 'type': 'ios-disc' },
                    { 'type': 'ios-disc-outline' },
                    { 'type': 'md-disc' },
                    { 'type': 'ios-document' },
                    { 'type': 'ios-document-outline' },
                    { 'type': 'md-document' },
                    { 'type': 'ios-done-all' },
                    { 'type': 'md-done-all' },
                    { 'type': 'ios-download' },
                    { 'type': 'ios-download-outline' },
                    { 'type': 'md-download' },
                    { 'type': 'logo-dribbble' },
                    { 'type': 'logo-dropbox' },
                    { 'type': 'ios-easel' },
                    { 'type': 'ios-easel-outline' },
                    { 'type': 'md-easel' },
                    { 'type': 'ios-egg' },
                    { 'type': 'ios-egg-outline' },
                    { 'type': 'md-egg' },
                    { 'type': 'logo-euro' },
                    { 'type': 'ios-exit' },
                    { 'type': 'ios-exit-outline' },
                    { 'type': 'md-exit' },
                    { 'type': 'ios-expand' },
                    { 'type': 'md-expand' },
                    { 'type': 'ios-eye' },
                    { 'type': 'ios-eye-outline' },
                    { 'type': 'md-eye' },
                    { 'type': 'ios-eye-off' },
                    { 'type': 'ios-eye-off-outline' },
                    { 'type': 'md-eye-off' },
                    { 'type': 'logo-facebook' },
                    { 'type': 'ios-fastforward' },
                    { 'type': 'ios-fastforward-outline' },
                    { 'type': 'md-fastforward' },
                    { 'type': 'ios-female' },
                    { 'type': 'md-female' },
                    { 'type': 'ios-filing' },
                    { 'type': 'ios-filing-outline' },
                    { 'type': 'md-filing' },
                    { 'type': 'ios-film' },
                    { 'type': 'ios-film-outline' },
                    { 'type': 'md-film' },
                    { 'type': 'ios-finger-print' },
                    { 'type': 'md-finger-print' },
                    { 'type': 'ios-flag' },
                    { 'type': 'ios-flag-outline' },
                    { 'type': 'md-flag' },
                    { 'type': 'ios-flame' },
                    { 'type': 'ios-flame-outline' },
                    { 'type': 'md-flame' },
                    { 'type': 'ios-flash' },
                    { 'type': 'ios-flash-outline' },
                    { 'type': 'md-flash' },
                    { 'type': 'ios-flask' },
                    { 'type': 'ios-flask-outline' },
                    { 'type': 'md-flask' },
                    { 'type': 'ios-flower' },
                    { 'type': 'ios-flower-outline' },
                    { 'type': 'md-flower' },
                    { 'type': 'ios-folder' },
                    { 'type': 'ios-folder-outline' },
                    { 'type': 'md-folder' },
                    { 'type': 'ios-folder-open' },
                    { 'type': 'ios-folder-open-outline' },
                    { 'type': 'md-folder-open' },
                    { 'type': 'ios-football' },
                    { 'type': 'ios-football-outline' },
                    { 'type': 'md-football' },
                    { 'type': 'logo-foursquare' },
                    { 'type': 'logo-freebsd-devil' },
                    { 'type': 'ios-funnel' },
                    { 'type': 'ios-funnel-outline' },
                    { 'type': 'md-funnel' },
                    { 'type': 'ios-game-controller-a' },
                    { 'type': 'ios-game-controller-a-outline' },
                    { 'type': 'md-game-controller-a' },
                    { 'type': 'ios-game-controller-b' },
                    { 'type': 'ios-game-controller-b-outline' },
                    { 'type': 'md-game-controller-b' },
                    { 'type': 'ios-git-branch' },
                    { 'type': 'md-git-branch' },
                    { 'type': 'ios-git-commit' },
                    { 'type': 'md-git-commit' },
                    { 'type': 'ios-git-compare' },
                    { 'type': 'md-git-compare' },
                    { 'type': 'ios-git-merge' },
                    { 'type': 'md-git-merge' },
                    { 'type': 'ios-git-network' },
                    { 'type': 'md-git-network' },
                    { 'type': 'ios-git-pull-request' },
                    { 'type': 'md-git-pull-request' },
                    { 'type': 'logo-github' },
                    { 'type': 'ios-glasses' },
                    { 'type': 'ios-glasses-outline' },
                    { 'type': 'md-glasses' },
                    { 'type': 'ios-globe' },
                    { 'type': 'ios-globe-outline' },
                    { 'type': 'md-globe' },
                    { 'type': 'logo-google' },
                    { 'type': 'logo-googleplus' },
                    { 'type': 'ios-grid' },
                    { 'type': 'ios-grid-outline' },
                    { 'type': 'md-grid' },
                    { 'type': 'logo-hackernews' },
                    { 'type': 'ios-hammer' },
                    { 'type': 'ios-hammer-outline' },
                    { 'type': 'md-hammer' },
                    { 'type': 'ios-hand' },
                    { 'type': 'ios-hand-outline' },
                    { 'type': 'md-hand' },
                    { 'type': 'ios-happy' },
                    { 'type': 'ios-happy-outline' },
                    { 'type': 'md-happy' },
                    { 'type': 'ios-headset' },
                    { 'type': 'ios-headset-outline' },
                    { 'type': 'md-headset' },
                    { 'type': 'ios-heart' },
                    { 'type': 'ios-heart-outline' },
                    { 'type': 'md-heart' },
                    { 'type': 'md-heart-outline' },
                    { 'type': 'ios-help' },
                    { 'type': 'md-help' },
                    { 'type': 'ios-help-buoy' },
                    { 'type': 'ios-help-buoy-outline' },
                    { 'type': 'md-help-buoy' },
                    { 'type': 'ios-help-circle' },
                    { 'type': 'ios-help-circle-outline' },
                    { 'type': 'md-help-circle' },
                    { 'type': 'ios-home' },
                    { 'type': 'ios-home-outline' },
                    { 'type': 'md-home' },
                    { 'type': 'logo-html5' },
                    { 'type': 'ios-ice-cream' },
                    { 'type': 'ios-ice-cream-outline' },
                    { 'type': 'md-ice-cream' },
                    { 'type': 'ios-image' },
                    { 'type': 'ios-image-outline' },
                    { 'type': 'md-image' },
                    { 'type': 'ios-images' },
                    { 'type': 'ios-images-outline' },
                    { 'type': 'md-images' },
                    { 'type': 'ios-infinite' },
                    { 'type': 'ios-infinite-outline' },
                    { 'type': 'md-infinite' },
                    { 'type': 'ios-information' },
                    { 'type': 'md-information' },
                    { 'type': 'ios-information-circle' },
                    { 'type': 'ios-information-circle-outline' },
                    { 'type': 'md-information-circle' },
                    { 'type': 'logo-instagram' },
                    { 'type': 'ios-ionic' },
                    { 'type': 'ios-ionic-outline' },
                    { 'type': 'md-ionic' },
                    { 'type': 'ios-ionitron' },
                    { 'type': 'ios-ionitron-outline' },
                    { 'type': 'md-ionitron' },
                    { 'type': 'logo-javascript' },
                    { 'type': 'ios-jet' },
                    { 'type': 'ios-jet-outline' },
                    { 'type': 'md-jet' },
                    { 'type': 'ios-key' },
                    { 'type': 'ios-key-outline' },
                    { 'type': 'md-key' },
                    { 'type': 'ios-keypad' },
                    { 'type': 'ios-keypad-outline' },
                    { 'type': 'md-keypad' },
                    { 'type': 'ios-laptop' },
                    { 'type': 'md-laptop' },
                    { 'type': 'ios-leaf' },
                    { 'type': 'ios-leaf-outline' },
                    { 'type': 'md-leaf' },
                    { 'type': 'ios-link' },
                    { 'type': 'ios-link-outline' },
                    { 'type': 'md-link' },
                    { 'type': 'logo-linkedin' },
                    { 'type': 'ios-list' },
                    { 'type': 'md-list' },
                    { 'type': 'ios-list-box' },
                    { 'type': 'ios-list-box-outline' },
                    { 'type': 'md-list-box' },
                    { 'type': 'ios-locate' },
                    { 'type': 'ios-locate-outline' },
                    { 'type': 'md-locate' },
                    { 'type': 'ios-lock' },
                    { 'type': 'ios-lock-outline' },
                    { 'type': 'md-lock' },
                    { 'type': 'ios-log-in' },
                    { 'type': 'md-log-in' },
                    { 'type': 'ios-log-out' },
                    { 'type': 'md-log-out' },
                    { 'type': 'ios-magnet' },
                    { 'type': 'ios-magnet-outline' },
                    { 'type': 'md-magnet' },
                    { 'type': 'ios-mail' },
                    { 'type': 'ios-mail-outline' },
                    { 'type': 'md-mail' },
                    { 'type': 'ios-mail-open' },
                    { 'type': 'ios-mail-open-outline' },
                    { 'type': 'md-mail-open' },
                    { 'type': 'ios-male' },
                    { 'type': 'md-male' },
                    { 'type': 'ios-man' },
                    { 'type': 'ios-man-outline' },
                    { 'type': 'md-man' },
                    { 'type': 'ios-map' },
                    { 'type': 'ios-map-outline' },
                    { 'type': 'md-map' },
                    { 'type': 'logo-markdown' },
                    { 'type': 'ios-medal' },
                    { 'type': 'ios-medal-outline' },
                    { 'type': 'md-medal' },
                    { 'type': 'ios-medical' },
                    { 'type': 'ios-medical-outline' },
                    { 'type': 'md-medical' },
                    { 'type': 'ios-medkit' },
                    { 'type': 'ios-medkit-outline' },
                    { 'type': 'md-medkit' },
                    { 'type': 'ios-megaphone' },
                    { 'type': 'ios-megaphone-outline' },
                    { 'type': 'md-megaphone' },
                    { 'type': 'ios-menu' },
                    { 'type': 'ios-menu-outline' },
                    { 'type': 'md-menu' },
                    { 'type': 'ios-mic' },
                    { 'type': 'ios-mic-outline' },
                    { 'type': 'md-mic' },
                    { 'type': 'ios-mic-off' },
                    { 'type': 'ios-mic-off-outline' },
                    { 'type': 'md-mic-off' },
                    { 'type': 'ios-microphone' },
                    { 'type': 'ios-microphone-outline' },
                    { 'type': 'md-microphone' },
                    { 'type': 'ios-moon' },
                    { 'type': 'ios-moon-outline' },
                    { 'type': 'md-moon' },
                    { 'type': 'ios-more' },
                    { 'type': 'ios-more-outline' },
                    { 'type': 'md-more' },
                    { 'type': 'ios-move' },
                    { 'type': 'md-move' },
                    { 'type': 'ios-musical-note' },
                    { 'type': 'ios-musical-note-outline' },
                    { 'type': 'md-musical-note' },
                    { 'type': 'ios-musical-notes' },
                    { 'type': 'ios-musical-notes-outline' },
                    { 'type': 'md-musical-notes' },
                    { 'type': 'ios-navigate' },
                    { 'type': 'ios-navigate-outline' },
                    { 'type': 'md-navigate' },
                    { 'type': 'ios-no-smoking' },
                    { 'type': 'ios-no-smoking-outline' },
                    { 'type': 'md-no-smoking' },
                    { 'type': 'logo-nodejs' },
                    { 'type': 'ios-notifications' },
                    { 'type': 'ios-notifications-outline' },
                    { 'type': 'md-notifications' },
                    { 'type': 'ios-notifications-off' },
                    { 'type': 'ios-notifications-off-outline' },
                    { 'type': 'md-notifications-off' },
                    { 'type': 'md-notifications-outline' },
                    { 'type': 'ios-nuclear' },
                    { 'type': 'ios-nuclear-outline' },
                    { 'type': 'md-nuclear' },
                    { 'type': 'ios-nutrition' },
                    { 'type': 'ios-nutrition-outline' },
                    { 'type': 'md-nutrition' },
                    { 'type': 'logo-octocat' },
                    { 'type': 'ios-open' },
                    { 'type': 'ios-open-outline' },
                    { 'type': 'md-open' },
                    { 'type': 'ios-options' },
                    { 'type': 'ios-options-outline' },
                    { 'type': 'md-options' },
                    { 'type': 'ios-outlet' },
                    { 'type': 'ios-outlet-outline' },
                    { 'type': 'md-outlet' },
                    { 'type': 'ios-paper' },
                    { 'type': 'ios-paper-outline' },
                    { 'type': 'md-paper' },
                    { 'type': 'ios-paper-plane' },
                    { 'type': 'ios-paper-plane-outline' },
                    { 'type': 'md-paper-plane' },
                    { 'type': 'ios-partly-sunny' },
                    { 'type': 'ios-partly-sunny-outline' },
                    { 'type': 'md-partly-sunny' },
                    { 'type': 'ios-pause' },
                    { 'type': 'ios-pause-outline' },
                    { 'type': 'md-pause' },
                    { 'type': 'ios-paw' },
                    { 'type': 'ios-paw-outline' },
                    { 'type': 'md-paw' },
                    { 'type': 'ios-people' },
                    { 'type': 'ios-people-outline' },
                    { 'type': 'md-people' },
                    { 'type': 'ios-person' },
                    { 'type': 'ios-person-outline' },
                    { 'type': 'md-person' },
                    { 'type': 'ios-person-add' },
                    { 'type': 'ios-person-add-outline' },
                    { 'type': 'md-person-add' },
                    { 'type': 'ios-phone-landscape' },
                    { 'type': 'md-phone-landscape' },
                    { 'type': 'ios-phone-portrait' },
                    { 'type': 'md-phone-portrait' },
                    { 'type': 'ios-photos' },
                    { 'type': 'ios-photos-outline' },
                    { 'type': 'md-photos' },
                    { 'type': 'ios-pie' },
                    { 'type': 'ios-pie-outline' },
                    { 'type': 'md-pie' },
                    { 'type': 'ios-pin' },
                    { 'type': 'ios-pin-outline' },
                    { 'type': 'md-pin' },
                    { 'type': 'ios-pint' },
                    { 'type': 'ios-pint-outline' },
                    { 'type': 'md-pint' },
                    { 'type': 'logo-pinterest' },
                    { 'type': 'ios-pizza' },
                    { 'type': 'ios-pizza-outline' },
                    { 'type': 'md-pizza' },
                    { 'type': 'ios-plane' },
                    { 'type': 'ios-plane-outline' },
                    { 'type': 'md-plane' },
                    { 'type': 'ios-planet' },
                    { 'type': 'ios-planet-outline' },
                    { 'type': 'md-planet' },
                    { 'type': 'ios-play' },
                    { 'type': 'ios-play-outline' },
                    { 'type': 'md-play' },
                    { 'type': 'logo-playstation' },
                    { 'type': 'ios-podium' },
                    { 'type': 'ios-podium-outline' },
                    { 'type': 'md-podium' },
                    { 'type': 'ios-power' },
                    { 'type': 'ios-power-outline' },
                    { 'type': 'md-power' },
                    { 'type': 'ios-pricetag' },
                    { 'type': 'ios-pricetag-outline' },
                    { 'type': 'md-pricetag' },
                    { 'type': 'ios-pricetags' },
                    { 'type': 'ios-pricetags-outline' },
                    { 'type': 'md-pricetags' },
                    { 'type': 'ios-print' },
                    { 'type': 'ios-print-outline' },
                    { 'type': 'md-print' },
                    { 'type': 'ios-pulse' },
                    { 'type': 'ios-pulse-outline' },
                    { 'type': 'md-pulse' },
                    { 'type': 'logo-python' },
                    { 'type': 'ios-qr-scanner' },
                    { 'type': 'md-qr-scanner' },
                    { 'type': 'ios-quote' },
                    { 'type': 'ios-quote-outline' },
                    { 'type': 'md-quote' },
                    { 'type': 'ios-radio' },
                    { 'type': 'ios-radio-outline' },
                    { 'type': 'md-radio' },
                    { 'type': 'ios-radio-button-off' },
                    { 'type': 'md-radio-button-off' },
                    { 'type': 'ios-radio-button-on' },
                    { 'type': 'md-radio-button-on' },
                    { 'type': 'ios-rainy' },
                    { 'type': 'ios-rainy-outline' },
                    { 'type': 'md-rainy' },
                    { 'type': 'ios-recording' },
                    { 'type': 'ios-recording-outline' },
                    { 'type': 'md-recording' },
                    { 'type': 'logo-reddit' },
                    { 'type': 'ios-redo' },
                    { 'type': 'ios-redo-outline' },
                    { 'type': 'md-redo' },
                    { 'type': 'ios-refresh' },
                    { 'type': 'md-refresh' },
                    { 'type': 'ios-refresh-circle' },
                    { 'type': 'ios-refresh-circle-outline' },
                    { 'type': 'md-refresh-circle' },
                    { 'type': 'ios-remove' },
                    { 'type': 'md-remove' },
                    { 'type': 'ios-remove-circle' },
                    { 'type': 'ios-remove-circle-outline' },
                    { 'type': 'md-remove-circle' },
                    { 'type': 'ios-reorder' },
                    { 'type': 'md-reorder' },
                    { 'type': 'ios-repeat' },
                    { 'type': 'md-repeat' },
                    { 'type': 'ios-resize' },
                    { 'type': 'md-resize' },
                    { 'type': 'ios-restaurant' },
                    { 'type': 'ios-restaurant-outline' },
                    { 'type': 'md-restaurant' },
                    { 'type': 'ios-return-left' },
                    { 'type': 'md-return-left' },
                    { 'type': 'ios-return-right' },
                    { 'type': 'md-return-right' },
                    { 'type': 'ios-reverse-camera' },
                    { 'type': 'ios-reverse-camera-outline' },
                    { 'type': 'md-reverse-camera' },
                    { 'type': 'ios-rewind' },
                    { 'type': 'ios-rewind-outline' },
                    { 'type': 'md-rewind' },
                    { 'type': 'ios-ribbon' },
                    { 'type': 'ios-ribbon-outline' },
                    { 'type': 'md-ribbon' },
                    { 'type': 'ios-rose' },
                    { 'type': 'ios-rose-outline' },
                    { 'type': 'md-rose' },
                    { 'type': 'logo-rss' },
                    { 'type': 'ios-sad' },
                    { 'type': 'ios-sad-outline' },
                    { 'type': 'md-sad' },
                    { 'type': 'logo-sass' },
                    { 'type': 'ios-school' },
                    { 'type': 'ios-school-outline' },
                    { 'type': 'md-school' },
                    { 'type': 'ios-search' },
                    { 'type': 'ios-search-outline' },
                    { 'type': 'md-search' },
                    { 'type': 'ios-send' },
                    { 'type': 'ios-send-outline' },
                    { 'type': 'md-send' },
                    { 'type': 'ios-settings' },
                    { 'type': 'ios-settings-outline' },
                    { 'type': 'md-settings' },
                    { 'type': 'ios-share' },
                    { 'type': 'ios-share-outline' },
                    { 'type': 'md-share' },
                    { 'type': 'ios-share-alt' },
                    { 'type': 'ios-share-alt-outline' },
                    { 'type': 'md-share-alt' },
                    { 'type': 'ios-shirt' },
                    { 'type': 'ios-shirt-outline' },
                    { 'type': 'md-shirt' },
                    { 'type': 'ios-shuffle' },
                    { 'type': 'md-shuffle' },
                    { 'type': 'ios-skip-backward' },
                    { 'type': 'ios-skip-backward-outline' },
                    { 'type': 'md-skip-backward' },
                    { 'type': 'ios-skip-forward' },
                    { 'type': 'ios-skip-forward-outline' },
                    { 'type': 'md-skip-forward' },
                    { 'type': 'logo-skype' },
                    { 'type': 'logo-snapchat' },
                    { 'type': 'ios-snow' },
                    { 'type': 'ios-snow-outline' },
                    { 'type': 'md-snow' },
                    { 'type': 'ios-speedometer' },
                    { 'type': 'ios-speedometer-outline' },
                    { 'type': 'md-speedometer' },
                    { 'type': 'ios-square' },
                    { 'type': 'ios-square-outline' },
                    { 'type': 'md-square' },
                    { 'type': 'md-square-outline' },
                    { 'type': 'ios-star' },
                    { 'type': 'ios-star-outline' },
                    { 'type': 'md-star' },
                    { 'type': 'ios-star-half' },
                    { 'type': 'md-star-half' },
                    { 'type': 'md-star-outline' },
                    { 'type': 'ios-stats' },
                    { 'type': 'ios-stats-outline' },
                    { 'type': 'md-stats' },
                    { 'type': 'logo-steam' },
                    { 'type': 'ios-stopwatch' },
                    { 'type': 'ios-stopwatch-outline' },
                    { 'type': 'md-stopwatch' },
                    { 'type': 'ios-subway' },
                    { 'type': 'ios-subway-outline' },
                    { 'type': 'md-subway' },
                    { 'type': 'ios-sunny' },
                    { 'type': 'ios-sunny-outline' },
                    { 'type': 'md-sunny' },
                    { 'type': 'ios-swap' },
                    { 'type': 'md-swap' },
                    { 'type': 'ios-switch' },
                    { 'type': 'ios-switch-outline' },
                    { 'type': 'md-switch' },
                    { 'type': 'ios-sync' },
                    { 'type': 'md-sync' },
                    { 'type': 'ios-tablet-landscape' },
                    { 'type': 'md-tablet-landscape' },
                    { 'type': 'ios-tablet-portrait' },
                    { 'type': 'md-tablet-portrait' },
                    { 'type': 'ios-tennisball' },
                    { 'type': 'ios-tennisball-outline' },
                    { 'type': 'md-tennisball' },
                    { 'type': 'ios-text' },
                    { 'type': 'ios-text-outline' },
                    { 'type': 'md-text' },
                    { 'type': 'ios-thermometer' },
                    { 'type': 'ios-thermometer-outline' },
                    { 'type': 'md-thermometer' },
                    { 'type': 'ios-thumbs-down' },
                    { 'type': 'ios-thumbs-down-outline' },
                    { 'type': 'md-thumbs-down' },
                    { 'type': 'ios-thumbs-up' },
                    { 'type': 'ios-thumbs-up-outline' },
                    { 'type': 'md-thumbs-up' },
                    { 'type': 'ios-thunderstorm' },
                    { 'type': 'ios-thunderstorm-outline' },
                    { 'type': 'md-thunderstorm' },
                    { 'type': 'ios-time' },
                    { 'type': 'ios-time-outline' },
                    { 'type': 'md-time' },
                    { 'type': 'ios-timer' },
                    { 'type': 'ios-timer-outline' },
                    { 'type': 'md-timer' },
                    { 'type': 'ios-train' },
                    { 'type': 'ios-train-outline' },
                    { 'type': 'md-train' },
                    { 'type': 'ios-transgender' },
                    { 'type': 'md-transgender' },
                    { 'type': 'ios-trash' },
                    { 'type': 'ios-trash-outline' },
                    { 'type': 'md-trash' },
                    { 'type': 'ios-trending-down' },
                    { 'type': 'md-trending-down' },
                    { 'type': 'ios-trending-up' },
                    { 'type': 'md-trending-up' },
                    { 'type': 'ios-trophy' },
                    { 'type': 'ios-trophy-outline' },
                    { 'type': 'md-trophy' },
                    { 'type': 'logo-tumblr' },
                    { 'type': 'logo-tux' },
                    { 'type': 'logo-twitch' },
                    { 'type': 'logo-twitter' },
                    { 'type': 'ios-umbrella' },
                    { 'type': 'ios-umbrella-outline' },
                    { 'type': 'md-umbrella' },
                    { 'type': 'ios-undo' },
                    { 'type': 'ios-undo-outline' },
                    { 'type': 'md-undo' },
                    { 'type': 'ios-unlock' },
                    { 'type': 'ios-unlock-outline' },
                    { 'type': 'md-unlock' },
                    { 'type': 'logo-usd' },
                    { 'type': 'ios-videocam' },
                    { 'type': 'ios-videocam-outline' },
                    { 'type': 'md-videocam' },
                    { 'type': 'logo-vimeo' },
                    { 'type': 'ios-volume-down' },
                    { 'type': 'md-volume-down' },
                    { 'type': 'ios-volume-mute' },
                    { 'type': 'md-volume-mute' },
                    { 'type': 'ios-volume-off' },
                    { 'type': 'md-volume-off' },
                    { 'type': 'ios-volume-up' },
                    { 'type': 'md-volume-up' },
                    { 'type': 'ios-walk' },
                    { 'type': 'md-walk' },
                    { 'type': 'ios-warning' },
                    { 'type': 'ios-warning-outline' },
                    { 'type': 'md-warning' },
                    { 'type': 'ios-watch' },
                    { 'type': 'md-watch' },
                    { 'type': 'ios-water' },
                    { 'type': 'ios-water-outline' },
                    { 'type': 'md-water' },
                    { 'type': 'logo-whatsapp' },
                    { 'type': 'ios-wifi' },
                    { 'type': 'ios-wifi-outline' },
                    { 'type': 'md-wifi' },
                    { 'type': 'logo-windows' },
                    { 'type': 'ios-wine' },
                    { 'type': 'ios-wine-outline' },
                    { 'type': 'md-wine' },
                    { 'type': 'ios-woman' },
                    { 'type': 'ios-woman-outline' },
                    { 'type': 'md-woman' },
                    { 'type': 'logo-wordpress' },
                    { 'type': 'logo-xbox' },
                    { 'type': 'logo-yahoo' },
                    { 'type': 'logo-yen' },
                    { 'type': 'logo-youtube' },
                    { 'type': 'ios-loading' }
                ]
            }
        },
        mounted () {
            this.list = this.search;
        },
        methods: {
            // 搜索
            upIcon (n) {
                let arrs = [];
                for (var i = 0; i < this.search.length; i++) {
                    if (this.search[i].type.indexOf(n) !== -1) {
                        arrs.push(this.search[i]);
                        this.list = arrs;
                    }
                }
            },
            iconChange (n) {
                if (this.$route.query.fodder === 'icon') {
                    /* eslint-disable */
                form_create_helper.set('icon', n)
                form_create_helper.close('icon');
            }
        }
    }
}
</script>

<style scoped lang="stylus">
    .iconBox
        background #fff

    .icons-item
        float: left;
        margin: 6px 6px 6px 0;
        width: 53px;
        text-align: center;
        list-style: none;
        cursor: pointer;
        height: 50px;
        color: #5c6b77;
        transition: all .2s ease;
        position: relative;
        padding-top: 10px;

        >>> .ivu-icon
            font-size: 32px !important;

    .trees-coadd
        width: 100%;
        height: 500px;
        border-radius: 4px;
        overflow: hidden;

    .scollhide
        width: 100%;
        height: 100%;
        overflow: auto;
        margin-left: 18px;
        padding: 10px 0 10px 0;
        box-sizing: border-box;

        .content
            font-size 12px

        .time
            font-size 12px
            color: #2d8cf0
</style>
