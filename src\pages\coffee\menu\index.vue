<template>
    <div>
        <Card :bordered="false" dis-hover>
            <PageHeader class="product_tabs" :title="$route.meta.title" hidden-breadcrumb>
                <div slot="content">
                    <Tabs @on-click="onClickTab">
                        <TabPane label="上架中" name="0" />
                        <TabPane label="已下架" name="1" />
                        <TabPane label="全部" name="all" />
                    </Tabs>
                </div>
            </PageHeader>
        </Card>
      <Row class="ivu-mt box-wrapper">
        <Col span="3" class="left-wrapper">
          <Menu :theme="theme3" width="auto">
            <MenuGroup>
                <MenuItem
                :name="999"
                  class="menu-item"
                  :key="999"
                  :class="999 === current ? 'showOn' : ''"
                  @click.native="bindMenuItem(selectedCategory, 999)"
              >
                 全部
              </MenuItem>
              <MenuItem
                :name="item.id"
                class="menu-item"
                :class="index === current ? 'showOn' : ''"
                v-for="(item, index) in categoryList"
                :key="index"
                @click.native="bindMenuItem(item, index)"
              >
              <div class="tabBox_img2">
                <img v-lazy="item.img" />
             </div>
                {{ item.name }}
                <div class="icon-box">
                    <Icon type="ios-more" size="24" @click.stop="showMenu(item)" />
                 </div>
              </MenuItem>
            </MenuGroup>
          </Menu>
        </Col>
        <Col span="21" ref="rightBox">
          <Card :bordered="false" dis-hover>
              <Form
                  ref="formValidate"
                  :model="formValidate"
                  :label-width="labelWidth"
                  :label-position="labelPosition"
                  @submit.native.prevent
              >
                  <Row type="flex" :gutter="24">
                      <Col v-bind="grid">
                          <FormItem label="商品搜索：" label-for="store_name">
                              <Input
                                  search
                                  enter-button
                                  placeholder="请输入商品名称"
                                  v-model="formValidate.store_name"
                                  @on-search="userSearchs"
                              />
                          </FormItem>
                      </Col>
                  </Row>
              </Form>
              <div class="btnbox">
                  <router-link :to="'/admin/serve/add_product'">
                      <Button type="primary" class="bnt mr15" icon="md-add">添加商品</Button>
                  </router-link>
                  <Button
                  type="success"
                  icon="md-add"
                  @click="addCategory"
                  >添加分类</Button
                >
                  <Button type="success" style="margin-left: 10px" @click="sync()">同步到所有店铺</Button>
              </div>
                <Table
                :columns="columns"
                :data="goodsList"
                ref="table"
                class="mt25"
                :loading="loading"
                highlight-row
                no-userFrom-text="暂无数据"
                no-filtered-userFrom-text="暂无筛选结果"
                >
                        <template slot-scope="{ row, index }" slot="img">
                            <viewer>
                                <div class="tabBox_img">
                                    <img v-lazy="row.img" />
                                </div>
                            </viewer>
                        </template>
                        <template slot-scope="{ row, index }" slot="is_status">
                            <i-switch
                                v-model="row.is_status"
                                :value="row.is_status"
                                :true-value="0"
                                :false-value="1"
                                @on-change="changeSwitch(row)"
                                size="large"
                            >
                                <span slot="open">上架</span>
                                <span slot="close">下架</span>
                            </i-switch>
                        </template>
                        <template slot-scope="{ row, index }" slot="action">
                            <a @click="sync(row)">同步</a>
                            <Divider type="vertical" />
                            <a @click="edit(row)">编辑</a>
                            <Divider type="vertical" />
                            <a @click="delte(row, '删除该商品', index)">删除</a>
                        </template>
                </Table>
                <div class="acea-row row-right page">
                <Page
                    :total="total"
                    :current="formValidate.page"
                    show-elevator
                    show-total
                    @on-change="pageChange"
                    :page-size="formValidate.limit"
                />
                </div>
          </Card>
        </Col>
      </Row>
        <!-- 添加 编辑分类-->
        <add-form ref="template"></add-form>
        <Drawer :title="title" :closable="false" v-model="value3">
            <div
             class="right-menu ivu-poptip-inner"
            >
            <div class="ivu-poptip-body" @click="labelEdit(1)">
              <div class="ivu-poptip-body-content">
                <div class="ivu-poptip-body-content-inner">编辑</div>
              </div>
            </div>
            <div
              class="ivu-poptip-body"
              @click="deleteSort()"
            >
              <div class="ivu-poptip-body-content">
                <div class="ivu-poptip-body-content-inner">删除</div>
              </div>
            </div>
          </div>
        </Drawer>
        <Modal
        v-model="modals"
            title="店铺列表"
            footerHide
            class="paymentFooter"
            scrollable
            width="900"
            @on-cancel="cancel"
        >
            <goods-list ref="goodslist" v-if="modals" :ischeckbox="true" @getProductId="getProductId"></goods-list>
        </Modal>
    </div>
  </template>
  
  <script>
  import { categoryListApiTest, productListApiTest, productDelApiTest, setProductApiTest, categoryDelApiTest, syncProduct } from '@/api/coffee';
  import goodsList from './goodsList';
  import addForm from "./addCategoryForm";
  export default {
      name: 'menuList',
      components: {
          goodsList,
          addForm
      },
      data() {
          return {
              title:'分类设置',
              value3:false,
              value:{},
              // 商品同步
              modals: false,
              productList: [],
              // 搜索
              store_name: '',
              shopList: [],
              categoryList: [],
              total: 0,
              loading: false,
              // {shop_id:1460889029063921666,category_id:1335872970110828547}
              formValidate: {
                  shop_id: '',
                  category_id: '',
                  store_name: '',
                  type: 0,
                  page: 1,
                  limit: 10,
              },
              // 搜索样式
              grid: {
                  xl: 7,
                  lg: 8,
                  md: 12,
                  sm: 24,
                  xs: 24,
              },
              theme3: "light",
              columns: [
                  {
                      title: '商品图',
                      slot: "img",
                      minWidth: 80
                  },
                  {
                      title: '分类',
                      key: 'category_name',
                      minWidth: 80
                  },
                  {
                      title: '园区价',
                      key: 'price',
                      minWidth: 80
                  },
                  {
                      title: '商品名称',
                      key: 'name',
                      minWidth: 150
                  },
                  {
                      title: "排序",
                      key: "sort",
                      sortable: true,
                      minWidth: 40,
                  },
                  {
                      title: "状态",
                      slot: "is_status",
                      width: 100,
                      filters: [
                          {
                              label: "上架",
                              value: 0,
                          },
                          {
                              label: "下架",
                              value: 1,
                          },
                      ],
                      filterMethod(value, row) {
                          return row.is_status === value;
                      },
                      filterMultiple: false,
                  },
                  {
                      title: '操作',
                      slot: 'action',
                      fixed: 'right',
                      minWidth: 100,
                      align: 'center'
                  }
              ],
              selectedCategory:{
                  id:'',
              },
              current: 999,
              current2: 999,
              goodsList: [
  
              ],
              productId:'',
          }
      },
      computed: {
          labelWidth() {
              return this.isMobile ? undefined : 75;
          },
          labelPosition() {
              return this.isMobile ? 'top' : 'right';
          },
      },
      mounted() {
          this.getList()
          this.getCategory()
      },
      methods: {
            // 显示标签小菜单
           showMenu(item) {
              this.value = item
              this.value3 = true
              this.title = item.name + '设置'
           },
          qx() {
            this.current2 = 999;
          },
            //编辑标签
        labelEdit() {
            this.value3 = false
			this.$refs.template.title = '编辑分类'
			this.$refs.template.isTemplate = true;
			this.$refs.template.getInfo(this.value.id);
        },
        sync(item) {
            if(item) {
                this.modals = true
                this.productId = item.id
            } else {
                syncProduct({'id':this.value.id}).then(res=>{
                    this.$Message.success(res.msg);
                })
                this.productId = ''
            }
        },
        deleteSort() {
			this.$Modal.confirm({
				title: '确定要删除该分类吗？',
				content: '删除该分类后将无法恢复，请谨慎操作！',
				loading: true,
				onOk: () => {
					categoryDelApiTest({'id':this.value.id}).then(res=>{
						this.$Message.success(res.msg);
						this.getCategory()
						this.$Modal.remove();
					})
				}
			});
        },
          getCategory() {
                categoryListApiTest({is_show:10}).then(res => {
                this.categoryList = res.data.list
            })
          },
          addCategory() {
            this.$refs.template.id = 0;
            this.$refs.template.isTemplate = true;
          },
          bindMenuItem(name, index) {
              this.current = index;
              this.formValidate.category_id = name.id;
              this.getList();
          },
          // 选择的商品
          unique(arr) {
              const res = new Map();
              return arr.filter((arr) => !res.has(arr.product_id) && res.set(arr.product_id, 1))
          },
          getProductId(productList) {
              this.modals = false;
              syncProduct({'shopId':productList,'productId':this.productId}).then(res=>{
                    this.$Message.success(res.msg);
              })
          },
          cancel() {
              this.modals = false;
          },
          getList() {
              this.loading = true
              productListApiTest(this.formValidate).then(res => {
                  this.goodsList = res.data.list
                  this.total = res.data.count
                  this.loading = false
              })
          },
          edit(row) {
              let routeUrl = this.$router.resolve({ path: "/admin/serve/add_product/" + row.id });
               window.open(routeUrl .href, '_blank');
          },
          getExpiresTime(expiresTime) {
              let nowTimeNum = Math.round(new Date() / 1000);
              let expiresTimeNum = expiresTime - nowTimeNum;
              return parseFloat(parseFloat(parseFloat(expiresTimeNum / 60) / 60) / 24);
          },
          // 添加
          add() {
              console.log(1)
          },
          // 修改状态
          changeSwitch(row) {
              let data = {
                  id: row.id,
                  is_status: row.is_status
              }
              setProductApiTest(data).then(async res => {
                  this.$Message.success(res.msg);
              }).catch(res => {
                  this.$Message.error(res.msg);
              })
          },
          delte(row, tit, num) {
              this.$Modal.confirm({
                  title: '确定要删除该商品吗？',
                  content: '删除该商品后将无法恢复，请谨慎操作！',
                  loading: true,
                  onOk: () => {
                    productDelApiTest({ 'id': row.id }).then(res => {
                          this.$Message.success(res.msg);
                          this.getList()
                          this.$Modal.remove();
                      })
                  }
              });
          },
          onClickTab(e) {
              this.formValidate.page = 1
              this.formValidate.type = e
              this.getList()
          },
          //分页
          pageChange(status) {
              this.formValidate.page = status;
              this.getList()
          },
          // 表格搜索
          userSearchs() {
              this.formValidate.page = 1;
              this.getList();
          }
      },
  }
  </script>
  
  <style lang="stylus" scoped>
  .tabBox_img2{
    width: 18px;
    height: 18px;
    border-radius: 2px;
    cursor: pointer;

    img {
        width: 100%;
        height: 100%;
    }
  }
  .tabBox_img {
      width: 36px;
      height: 36px;
      border-radius: 4px;
      cursor: pointer;
  
      img {
          width: 100%;
          height: 100%;
      }
  }
  .showOn {
    color: #2d8cf0;
    background: #f0faff;
    z-index: 2;
  }
  
  /deep/ .ivu-menu-vertical .ivu-menu-item-group-title {
    display: none;
  }
  
  /deep/ .ivu-menu-vertical.ivu-menu-light:after {
    display: none;
  }
  
  /deep/ .ivu-menu {
    z-index: 0 !important;
  }
  
  .left-wrapper {
    // height: 904px;
    background: #fff;
    border-right: 1px solid #dcdee2;
  }
  
  .menu-item {
    position: relative;
    display: flex;
    justify-content: space-between;
    word-break: break-all;
  
    .icon-box {
      z-index: 3;
      position: absolute;

      top: 50%;
      transform: translateY(-50%);
      display: none;
    }
  
    &:hover .icon-box {
      display: block;
    }
  
    .right-menu {
      z-index: 10000;
      position: absolute;
      right: 9px;
      top: 2px;
      width: auto;
      min-width: 121px;
    }
  }
  </style>
  