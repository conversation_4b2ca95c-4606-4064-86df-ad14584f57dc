<template>
    <div>
        <Row class="expand-row">
            <Col span="8">
                <span class="expand-key">商品总价：</span>
                <span class="expand-value" v-text="row.pay_price"></span>
            </Col>
            <Col span="8">
                <span class="expand-key">下单时间：</span>
                <span class="expand-value" v-text="row.create_time"></span>
            </Col>
            <Col span="8">
                <span class="expand-key">预约：</span>
                <span class="expand-value" v-text="row.reserve_time"></span>
            </Col>
        </Row>
        <Row>
            <Col span="8">
                <span class="expand-key">用户备注：</span>
                <span class="expand-value" v-text="row.remark?row.remark:'无'"></span>
            </Col>
            <Col span="8">
                <span class="expand-key">微信订单号：</span>
                <span class="expand-value" v-text="row.wx_ordernum?row.wx_ordernum:'无'"></span>
            </Col>
        </Row>
    </div>
</template>

<script>
    export default {
        name: 'table-expand',
        props: {
            row: Object
        }
    }
</script>

<style scoped>
    .expand-row{
        margin-bottom: 16px;
    }
</style>
