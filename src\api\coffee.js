// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import request from "@/plugins/request_coffee";

/**
 * @description 店铺 -- 列表
 * @param {Object} param params {Object} 传值参数
 */
export function shopListApi(params) {
    return request({
        url: "shop/get",
        method: "post",
        params,
    });
}

/**
 * @description 店铺 -- 修改
 * @param {Object} param params {Object} 传值参数
 */
export function shopUpdateApi(params) {
    return request({
        url: "shop/update",
        method: "post",
        params,
    });
}

/**
 * @description 店铺 -- 删除
 * @param {Object} param params {Object} 传值参数
 */
export function shopDelApi(params) {
    return request({
        url: "shop/del",
        method: "get",
        params,
    });
}

/**
 * @description 店铺 -- 详情
 * @param {Object} param params {Object} 传值参数
 */
export function shopGetInfoApi(params) {
    return request({
        url: "shop/getInfo",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 列表
 * @param {Object} param params {Object} 传值参数
 */
export function categoryListApi(params) {
    return request({
        url: "category/get",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 添加修改
 * @param {Object} param params {Object} 传值参数
 */
export function categoryUpdateApi(params) {
    return request({
        url: "category/update",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 详情
 * @param {Object} param params {Object} 传值参数
 */
export function categoryGetInfoApi(params) {
    return request({
        url: "category/getInfo",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 删除
 * @param {Object} param params {Object} 传值参数
 */
export function categoryDelApi(params) {
    return request({
        url: "category/del",
        method: "get",
        params,
    });
}

/**
 * @description 商品分类 -- 状态改变
 * @param {Object} param params {Object} 传值参数
 */
export function setCategoryApi(params) {
    return request({
        url: "category/set",
        method: "get",
        params,
    });
}

/**
 * @description 商品分类 -- 列表
 * @param {Object} param params {Object} 传值参数
 */
export function productListApi(params) {
    return request({
        url: "product/get",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 添加修改
 * @param {Object} param params {Object} 传值参数
 */
export function productUpdateApi(params) {
    return request({
        url: "product/update",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 详情
 * @param {Object} param params {Object} 传值参数
 */
export function productGetInfoApi(params) {
    return request({
        url: "product/getInfo",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 删除
 * @param {Object} param params {Object} 传值参数
 */
export function productDelApi(params) {
    return request({
        url: "product/del",
        method: "get",
        params,
    });
}

/**
 * @description 商品分类 -- 状态改变
 * @param {Object} param params {Object} 传值参数
 */
export function setProductApi(params) {
    return request({
        url: "product/set",
        method: "get",
        params,
    });
}

/**
 * @description 店员下单商品列表 -- 详情
 * @param {Object} param params {Object} 传值参数
 */
export function categoryProductList(params) {
    return request({
        url: "shop/categoryProductList",
        method: "post",
        params,
    });
}

/**
 * @description 店员下单商品属性 -- 详情
 * @param {Object} param params {Object} 传值参数
 */
export function productAttrInfo(params) {
    return request({
        url: "product/info",
        method: "post",
        params,
    });
}

/**
 * @description 店员购物车 -- 详情
 * @param {Object} param params {Object} 传值参数
 */
export function cashierCartList(params) {
    return request({
        url: "cashier/cashierCartList",
        method: "post",
        params,
    });
}

/**
 * @description 店员创建订单 -- 详情
 * @param {Object} param params {Object} 传值参数
 */
export function cashierCreateOrder(params) {
    return request({
        url: "cashier/cashierCreateOrder",
        method: "post",
        params,
    });
}

/**
 * @description 店员清空或删除购物车 -- 详情
 * @param {Object} param params {Object} 传值参数
 */
export function cashierCartDel(params) {
    return request({
        url: "cashier/cashierCartDel",
        method: "post",
        params,
    });
}

/**
 * @description 店员信息获取 -- 详情
 * @param {Object} param params {Object} 传值参数
 */
export function cashierInfo(params) {
    return request({
        url: "shop/cashierInfo",
        method: "post",
        params,
    });
}

/**
 * @description 购物车加减
 * @param {Object} param params {Object} 传值参数
 */
export function cashierCartNum(params) {
    return request({
        url: "cashier/cashierCartNum",
        method: "post",
        params,
    });
}

/**
 * @description 其他支付
 * @param {Object} param params {Object} 传值参数
 */
export function cashierPay(params) {
    return request({
        url: "order/orderCreate",
        method: "post",
        params,
    });
}
/**
 * @description 余额支付
 * @param {Object} param params {Object} 传值参数
 */
export function orderYueCreate(params) {
    return request({
        url: "order/orderYueCreate",
        method: "post",
        params,
    });
}

/**
 * @description 积分订单数据--列表
 * @param {Object} param data {Object} 传值参数
 */
export function integralGetOrdes_v2 (data) {
    return request({
        url: 'marketing/integral/order/chart',
        method: 'get',
        params: data
    });
};

/**
 * @description 订单数据--列表
 * @param {Object} param data {Object} 传值参数
 */
export function getOrdes_v2(data) {
    return request({

        url: '/order/chart',
        method: 'get',
        params: data
    });
};

/**
 * @description 订单管理--列表
 * @param {Object} param data {Object} 传值参数
 */
export function orderList(data) {
    return request({
        url: '/order/list',
        method: 'get',
        params: data
    });
};

/**
 * @description 订单管理--打印
 * @param {Object} param data {Object} 传值参数
 */
export function printOrder(params) {
    return request({
        url: '/order/print',
        method: "post",
        params
    });
};

/**
 * @description 核销订单--打印
 * @param {Object} param data {Object} 传值参数
 */
export function wirteOrder(params) {
    return request({
        url: '/csu/writeOrder',
        method: "post",
        params
    });
};

/**
 * @description 订单管理--作废
 * @param {Object} param data {Object} 传值参数
 */
export function invalidOrder(params) {
    return request({
        url: '/order/invalidOrder',
        method: "post",
        params
    });
};

/**
 * @description 优惠券--列表
 * @param {Object} param data {Object} 传值参数
 */
export function couponList(params) {
    return request({
        url: '/coupon/list',
        method: "post",
        params
    });
};

/**
 * @description 优惠券--详情
 * @param {Object} param data {Object} 传值参数
 */
export function couponGetInfoApi(params) {
    return request({
        url: '/coupon/getInfo',
        method: "post",
        params
    });
};

/**
 * @description 优惠券--修改
 * @param {Object} param data {Object} 传值参数
 */
export function couponUpdateApi(params) {
    return request({
        url: '/coupon/update',
        method: "post",
        params
    });
};

/**
 * @description 优惠券--删除
 * @param {Object} param data {Object} 传值参数
 */
export function couponDel(params) {
    return request({
        url: '/coupon/delete',
        method: "post",
        params
    });
};


/**
 * @description 余额记录管理--列表
 * @param {Object} param data {Object} 传值参数
 */
export function billListApi(data) {
    return request({
        baseURL:'127.0.0.1',
        url: `/finance/list`,
        method: 'get',
        params: data
    });
}


/**
 * 测试版
 */
/**
 * @description 商品分类 -- 列表
 * @param {Object} param params {Object} 传值参数
 */
export function categoryListApiTest(params) {
    return request({
        url: "test/cateList",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 列表
 * @param {Object} param params {Object} 传值参数
 */
export function productListApiTest(params) {
    return request({
        url: "test/productList",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 删除
 * @param {Object} param params {Object} 传值参数
 */
export function productDelApiTest(params) {
    return request({
        url: "test/productDel",
        method: "get",
        params,
    });
}

/**
 * @description 商品分类 -- 状态改变
 * @param {Object} param params {Object} 传值参数
 */
export function setProductApiTest(params) {
    return request({
        url: "test/productSet",
        method: "get",
        params,
    });
}

/**
 * @description 商品分类 -- 添加修改
 * @param {Object} param params {Object} 传值参数
 */
export function productUpdateApiTest(params) {
    return request({
        url: "test/productUpdate",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 详情
 * @param {Object} param params {Object} 传值参数
 */
export function productGetInfoApiTest(params) {
    return request({
        url: "test/getInfo",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 添加修改
 * @param {Object} param params {Object} 传值参数
 */
export function categoryUpdateApiTest(params) {
    return request({
        url: "test/cateUpdate",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 详情
 * @param {Object} param params {Object} 传值参数
 */
export function categoryGetInfoApiTest(params) {
    return request({
        url: "test/cateGetInfo",
        method: "post",
        params,
    });
}

/**
 * @description 商品分类 -- 删除
 * @param {Object} param params {Object} 传值参数
 */
export function categoryDelApiTest(params) {
    return request({
        url: "test/cateDel",
        method: "get",
        params,
    });
}

/**
 * @description 商品分类 -- 删除
 * @param {Object} param params {Object} 传值参数
 */
export function syncProduct(params) {
    return request({
        url: "test/syncProduct",
        method: "get",
        params,
    });
}

/**
 * @description 店铺 -- 列表
 * @param {Object} param params {Object} 传值参数
 */
export function shopListApiTest(params) {
    return request({
        url: "test/shopGet",
        method: "post",
        params,
    });
}

/**
 * @description 店铺 -- 列表
 * @param {Object} param params {Object} 传值参数
 */
export function singleShopInfo(params) {
    return request({
        url: "single/shopInfo",
        method: "post",
        params,
    });
}

/**
 * @description 店铺 -- 修改
 * @param {Object} param params {Object} 传值参数
 */
export function singleShopUpdate(params) {
    return request({
        url: "single/updateShopInfo",
        method: "post",
        params,
    });
}

/**
 * @description 酒店--列表
 * @param {Object} param data {Object} 传值参数
 */
export function hotelListApi(data) {
    return request({
        baseURL:'127.0.0.1',
        url: `/common/hotel/getList`,
        method: 'get',
        params: data
    });
}

/**
 * @description 酒店打印--列表
 * @param {Object} param data {Object} 传值参数
 */
export function hotelPrintApi(data) {
    return request({
        baseURL:'127.0.0.1',
        url: `/common/hotel/print`,
        method: 'post',
        params: data
    });
}

/**
 * @description 更改支付类型
 * @param {Object} param params {Object} 传值参数
 */
export function updatePayPalType(params) {
    return request({
        url: "order/updatePayPalType",
        method: "post",
        params,
    });
}