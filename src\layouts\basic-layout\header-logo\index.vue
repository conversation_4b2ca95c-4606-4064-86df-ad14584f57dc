<template>
    <i-link class="i-layout-header-logo" to="/admin/coffee/transaction">
        <!--<img :src="logoSmall" v-if="isMobile">-->
        <!--<img :src="logo" v-else-if="headerTheme === 'light'">-->
        <!--<img :src="logoSmall" v-else-if="menuCollapse" alt="">-->
        <img :src="logo">
    </i-link>
</template>
<script>
    import { mapState } from 'vuex';
    export default {
        name: 'iHeaderLogo',
        computed: {
            ...mapState('admin/layout', [
                'isMobile',
                'headerTheme',
                'menuCollapse'
            ])
        },
        data () {
            return {
                logo: 'https://pic.imgdb.cn/item/64337dca0d2dde577769a240.png',
                logoSmall: 'https://pic.imgdb.cn/item/64337dca0d2dde577769a240.png'
            };
        },
        mounted () {
            this.getLogo();
        },
        methods: {
            getLogo () {
                this.$store.dispatch('admin/db/get', {
                    dbName: 'sys',
                    path: 'user.info',
                    user: true
                }).then(res => {
                    this.logo ='https://pic.imgdb.cn/item/64337dca0d2dde577769a240.png';
                    this.logoSmall = 'https://pic.imgdb.cn/item/64337dca0d2dde577769a240.png';
                });
            }
        }
    }
</script>
<style scoped>
    .i-layout-header-logo-stick.small_logo{
        width:80px;
    }
</style>
