// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
export default {
    namespaced: true,
    state: {
        shopName:"", // 店铺名称
        cateName:"", // 分类名称
        productName:"",
        cateId:"",
        shopId:"",
        productId:"",
    },
    mutations: {
     
        /**
         * @description 设置店铺名称
         * @param {Object} state vuex state
         * @param {String} shopName 设备类型
         */
        setShopName(state, shopName) {
            state.shopName = shopName;
        },

        /**
         * @description 设置分类名称
         * @param {Object} state vuex state
         * @param {String} cateName 分类
         */
        setCateName(state, cateName) {
            state.cateName = cateName;
        },

        /**
         * @description 设置商品名称
         * @param {Object} state vuex state
         * @param {String} productName 分类
         */
        setProductName(state, productName) {
            state.productName = productName;
        },

        /**
         * @description 设置店铺Id
         * @param {Object} state vuex state
         * @param {String} cateId 分类
         */
        setShopId(state, shopId) {
            state.shopId = shopId;
        },

        /**
         * @description 设置分类Id
         * @param {Object} state vuex state
         * @param {String} cateId 分类
         */
        setCateId(state, cateId) {
            state.cateId = cateId;
        },

        /**
         * @description 设置商品ID
         * @param {Object} state vuex state
         * @param {String} productId 分类
         */
        setProductId(state, productId) {
            state.productId = productId;
        },
                

    },
    actions: {

    },
};
