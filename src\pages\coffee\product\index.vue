<template>
  <div>
	<Card :bordered="false" dis-hover>
		<PageHeader class="product_tabs" :title="$route.meta.title" hidden-breadcrumb>
			<div slot="content">
				<Tabs @on-click="onClickTab">
				    <TabPane label="上架中" name="0" />
					<TabPane label="已下架" name="1" />
					<TabPane label="全部" name="all" />
				</Tabs>
			</div>
		</PageHeader>
	</Card>
    <Row class="ivu-mt box-wrapper">
      <Col span="3" class="left-wrapper">
        <Menu :theme="theme3" width="auto">
          <MenuGroup>
		  	<MenuItem
			   :name="999"
				class="menu-item"
				:class="999 === current ? 'showOn' : ''"
				@click.native="bindMenuItem(selectedCategory, 999)"
			>
			   全部
			</MenuItem>
            <MenuItem
              :name="item.id"
              class="menu-item"
              :class="index === current ? 'showOn' : ''"
              v-for="(item, index) in categoryList"
              :key="index"
              @click.native="bindMenuItem(item, index)"
            >
              {{ item.name }}
            </MenuItem>
          </MenuGroup>
        </Menu>
      </Col>
      <Col span="21" ref="rightBox">
        <Card :bordered="false" dis-hover>

			<Form
				ref="formValidate"
				:model="formValidate"
				:label-width="labelWidth"
				:label-position="labelPosition"
				@submit.native.prevent
			>
				<Row type="flex" :gutter="24">
					<Col v-bind="grid">
						<FormItem label="店铺：" label-for="shop_id">
							<Select v-model="formValidate.shop_id" placeholder="请选择" clearable @on-change="userSearchs">
								<Option v-for="(item, index) in shopList" :value="item.id" :key="index">{{ item.name }}</Option>
							</Select>
						</FormItem>
					</Col>
					<Col v-bind="grid">
						<FormItem label="商品搜索：" label-for="store_name">
							<Input
								search
								enter-button
								placeholder="请输入商品名称"
								v-model="formValidate.store_name"
								@on-search="userSearchs"
							/>
						</FormItem>
					</Col>
				</Row>
			</Form>
			<div class="btnbox">
				<router-link :to="'/admin/coffee/add_product'">
					<Button type="primary" class="bnt mr15" icon="md-add">添加商品</Button>
				</router-link>
				<Button type="success" @click="modals = true">同步商品</Button>
			</div>
          <Table
            :columns="columns"
            :data="goodsList"
            ref="table"
            class="mt25"
            :loading="loading"
            highlight-row
            no-userFrom-text="暂无数据"
            no-filtered-userFrom-text="暂无筛选结果"
          >
					<template slot-scope="{ row, index }" slot="img">
						<viewer>
							<div class="tabBox_img">
								<img v-lazy="row.img" />
							</div>
						</viewer>
					</template>
					<template slot-scope="{ row, index }" slot="is_status">
						<i-switch
							v-model="row.is_status"
							:value="row.is_status"
							:true-value="0"
							:false-value="1"
							@on-change="changeSwitch(row)"
							size="large"
						>
							<span slot="open">上架</span>
							<span slot="close">下架</span>
						</i-switch>
					</template>
					<template slot-scope="{ row, index }" slot="action">
						<a @click="edit(row)">编辑</a>
						<Divider type="vertical" />
						<a @click="delte(row, '删除该商品', index)">删除</a>
					</template>
          </Table>
          <div class="acea-row row-right page">
            <Page
              :total="total"
			  :current="formValidate.page"
              show-elevator
              show-total
              @on-change="pageChange"
              :page-size="formValidate.limit"
            />
          </div>
        </Card>
      </Col>
    </Row>
  </div>
</template>

<script>
import Setting from '@/setting';
import { categoryListApi, shopListApi, productListApi, productDelApi, setProductApi } from '@/api/coffee';
import goodsList from './goodsList';
export default {
	name: 'storeList',
	components: {
		goodsList
	},
	data() {
		return {
			// 商品同步
			modals: false,
			productList: [],
			// 搜索
			store_name: '',
			shopList: [],
			categoryList: [],
			BaseURL: Setting.apiBaseURL.replace(/adminapi/, "store/home/"),
			total: 0,
			loading: false,
			// {shop_id:1460889029063921666,category_id:1335872970110828547}
			formValidate: {
				shop_id: '',
				category_id: '',
				store_name: '',
				type: 0,
				page: 1,
				limit: 10,
			},
			// 搜索样式
			grid: {
				xl: 7,
				lg: 8,
				md: 12,
				sm: 24,
				xs: 24,
			},
			theme3: "light",
			columns: [
				{
					title: '商品图',
					slot: "img",
					minWidth: 80
				},
				{
					title: '门店',
					key: 'shop_name',
					minWidth: 80
				},
				{
					title: '分类',
					key: 'category_name',
					minWidth: 80
				},
				{
					title: '园区价',
					key: 'price',
					minWidth: 80
				},
				{
					title: '商品名称',
					key: 'name',
					minWidth: 150
				},
				{
					title: "排序",
					key: "sort",
					sortable: true,
					minWidth: 40,
				},
				{
					title: "状态",
					slot: "is_status",
					width: 100,
					filters: [
						{
							label: "上架",
							value: 0,
						},
						{
							label: "下架",
							value: 1,
						},
					],
					filterMethod(value, row) {
						return row.is_status === value;
					},
					filterMultiple: false,
				},
				{
					title: '操作',
					slot: 'action',
					fixed: 'right',
					minWidth: 220,
					align: 'center'
				}
			],
			selectedCategory:{
				id:'',
			},
			current: 999,
			goodsList: [

			]
		}
	},
	computed: {
		labelWidth() {
			return this.isMobile ? undefined : 75;
		},
		labelPosition() {
			return this.isMobile ? 'top' : 'right';
		},
	},
	mounted() {
		this.getList()
		shopListApi().then(res => {
			this.shopList = res.data.list
		})
		categoryListApi({is_show:10}).then(res => {
			this.categoryList = res.data.list
		})
	},
	methods: {
		bindMenuItem(name, index) {
			this.current = index;
			this.formValidate.category_id = name.id;
			this.getList();
		},
		// 选择的商品
		unique(arr) {
			const res = new Map();
			return arr.filter((arr) => !res.has(arr.product_id) && res.set(arr.product_id, 1))
		},
		getProductId(productList) {
			this.modals = false;
			this.productList = this.unique(this.productList.concat(productList));
			console.log(this.productList)
		},
		cancel() {
			this.modals = false;
		},
		getList() {
			this.loading = true
			productListApi(this.formValidate).then(res => {
				this.goodsList = res.data.list
				this.total = res.data.count
				this.loading = false
			})
		},
		edit(row) {
			let routeUrl = this.$router.resolve({ path: "/admin/coffee/add_product/" + row.id });
			 window.open(routeUrl .href, '_blank');
		},
		getExpiresTime(expiresTime) {
			let nowTimeNum = Math.round(new Date() / 1000);
			let expiresTimeNum = expiresTime - nowTimeNum;
			return parseFloat(parseFloat(parseFloat(expiresTimeNum / 60) / 60) / 24);
		},
		// 添加
		add() {
			console.log(1)
		},
		// 修改状态
		changeSwitch(row) {
			let data = {
				id: row.id,
				is_status: row.is_status
			}
			setProductApi(data).then(async res => {
				this.$Message.success(res.msg);
			}).catch(res => {
				this.$Message.error(res.msg);
			})
		},
		delte(row, tit, num) {
			this.$Modal.confirm({
				title: '确定要删除该商品吗？',
				content: '删除该商品后将无法恢复，请谨慎操作！',
				loading: true,
				onOk: () => {
					productDelApi({ 'id': row.id }).then(res => {
						this.$Message.success(res.msg);
						this.getList()
						this.$Modal.remove();
					})
				}
			});
		},
		onClickTab(e) {
			this.formValidate.page = 1
			this.formValidate.type = e
			this.getList()
		},
		//分页
		pageChange(status) {
			this.formValidate.page = status;
			this.getList()
		},
		// 表格搜索
		userSearchs() {
			// 分类搜索
			if (this.formValidate.shop_id) {
				categoryListApi({ shop_id: this.formValidate.shop_id,is_show: 10 }).then(res => {
					this.categoryList = res.data.list
				})
			}
			this.formValidate.page = 1;
			this.getList();
		}
	},
}
</script>

<style lang="stylus" scoped>
.tabBox_img {
	width: 36px;
	height: 36px;
	border-radius: 4px;
	cursor: pointer;

	img {
		width: 100%;
		height: 100%;
	}
}
.showOn {
  color: #2d8cf0;
  background: #f0faff;
  z-index: 2;
}

/deep/ .ivu-menu-vertical .ivu-menu-item-group-title {
  display: none;
}

/deep/ .ivu-menu-vertical.ivu-menu-light:after {
  display: none;
}

/deep/ .ivu-menu {
  z-index: 0 !important;
}

.left-wrapper {
  // height: 904px;
  background: #fff;
  border-right: 1px solid #dcdee2;
}

.menu-item {
  position: relative;
  display: flex;
  justify-content: space-between;
  word-break: break-all;

  .icon-box {
    z-index: 3;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: none;
  }

  &:hover .icon-box {
    display: block;
  }

  .right-menu {
    z-index: 10;
    position: absolute;
    right: -106px;
    top: -11px;
    width: auto;
    min-width: 121px;
  }
}
</style>
