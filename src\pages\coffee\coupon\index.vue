<template>
    <div>
        <div class="i-layout-page-header">
            <PageHeader class="product_tabs" title="用户优惠券列表" hidden-breadcrumb></PageHeader>
        </div>
        <Card :bordered="false" dis-hover class="ivu-mt">
            <Form ref="tableFrom" :model="tableFrom"  :label-width="labelWidth" :label-position="labelPosition" @submit.native.prevent>
                <Row type="flex" :gutter="24">
                    <Col v-bind="grid">
                        <FormItem label="状态：" label-for="status">
                            <Select v-model="tableFrom.status" placeholder="请选择" clearable  @on-change="userSearchs">
                                <Option value="2">已过期</Option>
                                <Option value="1">已使用</Option>
                                <Option value="0">未使用</Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col v-bind="grid">
                        <FormItem label="手机号搜索："  label-for="phone">
                            <Input search enter-button  v-model="tableFrom.phone" placeholder="请输入手机号" @on-search="userSearchs"/>
                        </FormItem>
                    </Col>
                </Row>
            </Form>
            <Table :columns="columns1" :data="tableList" ref="table" class="mt25"
                   :loading="loading" highlight-row
                   no-userFrom-text="暂无数据"
                   no-filtered-userFrom-text="暂无筛选结果">

                <template slot-scope="{ row, index }" slot="price">
                   <span v-if="row.pay_type === 1">{{row.price}}</span>
                   <span v-else-if="row.pay_type === 0">{{row.price}}元</span>
                   <span v-else>{{row.price}}元</span>
                </template>

                <template slot-scope="{ row, index }" slot="pay_type">
                   <span v-if="row.pay_type === 1">折扣券</span>
                   <span v-else>现金券</span>
                </template>
                <template slot-scope="{ row, index }" slot="time">
                    {{row.start_time}} ~ {{row.end_time}}
                </template>

                <template slot-scope="{ row, index }" slot="action">
                    <a @click="receive(row)">修改状态</a>
                    <Divider type="vertical"/>
                    <a @click="couponDel(row)">删除</a>
                </template>


            </Table>
            <div class="acea-row row-right page">
                <Page :total="total" :current="tableFrom.page" show-elevator show-total @on-change="pageChange"
                      :page-size="tableFrom.limit"/>
            </div>
        </Card>
        <!-- 添加 编辑表单-->
        <add-form ref="template"></add-form>
    </div>
</template>

<script>
    import { mapState } from 'vuex';
    import { delCouponReleased, couponStatusApi } from '@/api/marketing';
    import { couponList,couponDel } from '@/api/coffee';
    import { formatDate } from '@/utils/validate';
    import addForm from "./create";
    export default {
        name: 'coffeeCoupon',
        components: {
            addForm
        },
        filters: {
            formatDate (time) {
                if (time !== 0) {
                    let date = new Date(time * 1000);
                    return formatDate(date, 'yyyy-MM-dd hh:mm');
                }
            }
        },
        data () {
            return {
                grid: {
                    xl: 7,
                    lg: 7,
                    md: 12,
                    sm: 24,
                    xs: 24
                },
                loading: false,
                columns1: [
                    {
                        title: 'ID',
                        key: 'id',
                        width: 80
                    },
                    {
                        title: '优惠券名称',
                        key: 'name',
                        minWidth: 120,
                    },
                    {
                        title: '优惠券类型',
                        key: 'type',
                        minWidth: 80
                    },
                    {
                        title: '手机号',
                        key: 'phone',
                        minWidth: 120
                    },
                    {
                        title: '面值',
                        slot: 'price',
                        minWidth: 80
                    },
                    {
                        title: '支付方式',
                        slot: 'pay_type',
                        minWidth: 80
                    },
                    {
                        title: '状态',
                        key: 'status',
                        minWidth: 80
                    },
                    {
                        title: '有效期',
                        slot:'time',
                        minWidth: 260
                    },
                    {
                        title: '创建时间',
                        key: 'create_time',
                        minWidth: 100
                    },
                    {
                        title: '操作',
                        slot: 'action',
                        fixed: 'right',
                        minWidth: 200
                    }
                ],
                tableFrom: {
                    status: '',
                    phone: '',
                    page: 1,
                    limit: 15
                },
                receive_type:'',
                tableList: [],
                total: 0,
                FromData: null,
            }
        },
        created () {
            this.getList();
        },
        computed: {
            ...mapState('admin/layout', [
                'isMobile'
            ]),
            labelWidth () {
                return this.isMobile ? undefined : 90;
            },
            labelPosition () {
                return this.isMobile ? 'top' : 'left';
            }
        },
        methods: {
            // 状态
            receive (row) {
                this.$refs.template.title = '优惠券状态修改'
                this.$refs.template.isTemplate = true;
                this.$refs.template.getInfo(row.id);
            },
            // 列表
            getList () {
                this.loading = true;
                this.tableFrom.status = this.tableFrom.status || '';
                couponList(this.tableFrom).then(async res => {
                    let data = res.data
                    console.log(data)
                    this.tableList = data.list;
                    this.total = res.data.count;
                    this.loading = false;
                }).catch(res => {
                    this.loading = false;
                    this.$Message.error(res.msg);
                })
            },
            // 删除
            couponDel (row) {
                this.$Modal.confirm({
                    title: '确定要删除该优惠券吗？',
                    content: '删除该优惠券后将无法恢复，请谨慎操作！',
                    loading: true,
                    onOk: () => {
                        couponDel({ 'id': row.id }).then(res => {
                            this.$Message.success(res.msg);
                            this.getList()
                            this.$Modal.remove();
                        })
                    }
			});
            },
            pageChange (index) {
                this.tableFrom.page = index;
                this.getList();
            },
            // 表格搜索
            userSearchs () {
                this.tableFrom.page = 1;
                this.getList();
            }
        }
    }
</script>

<style scoped lang="stylus">
    .fa
        color #0a6aa1
        display block
    .sheng
       color #ff0000
       display block
    .tabBox_img
        width 36px
        height 36px
        border-radius:4px
        cursor pointer
        img
            width 100%
            height 100%

</style>
