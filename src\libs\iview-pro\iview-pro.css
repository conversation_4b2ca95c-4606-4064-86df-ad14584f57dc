.ivu-block {
    display: block
}

.ivu-inline {
    display: inline
}

.ivu-inline-block {
    display: inline-block
}

.ivu-text-center {
    text-align: center
}

.ivu-text-left {
    text-align: left
}

.ivu-text-right {
    text-align: right
}

.ivu-fl {
    float: left
}

.ivu-fr {
    float: right
}

.ivu-clearfix:after, .ivu-clearfix:before {
    display: table;
    content: ""
}

.ivu-clearfix:after {
    clear: both
}

.ivu-line-clamp {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden
}

.ivu-b {
    border: 1px solid #e8eaec
}

.ivu-bt {
    border-top: 1px solid #e8eaec
}

.ivu-br {
    border-right: 1px solid #e8eaec
}

.ivu-bb {
    border-bottom: 1px solid #e8eaec
}

.ivu-bl {
    border-left: 1px solid #e8eaec
}

.ivu-m-0 {
    margin: 0 !important
}

.ivu-mt-0 {
    margin-top: 0 !important
}

.ivu-mr-0 {
    margin-right: 0 !important
}

.ivu-mb-0 {
    margin-bottom: 0 !important
}

.ivu-ml-0 {
    margin-left: 0 !important
}

.ivu-m-8 {
    margin: 8px !important
}

.ivu-mt-8 {
    margin-top: 8px !important
}

.ivu-mr-8 {
    margin-right: 8px !important
}

.ivu-mb-8 {
    margin-bottom: 8px !important
}

.ivu-ml-8 {
    margin-left: 8px !important
}

.ivu-p-0 {
    padding: 0 !important
}

.ivu-pt-0 {
    padding-top: 0 !important
}

.ivu-pr-0 {
    padding-right: 0 !important
}

.ivu-pb-0 {
    padding-bottom: 0 !important
}

.ivu-pl-0 {
    padding-left: 0 !important
}

.ivu-p-8 {
    padding: 8px !important
}

.ivu-pt-8 {
    padding-top: 8px !important
}

.ivu-pr-8 {
    padding-right: 8px !important
}

.ivu-pb-8 {
    padding-bottom: 8px !important
}

.ivu-pl-8 {
    padding-left: 8px !important
}

.ivu-m, .ivu-m-16 {
    margin: 16px !important
}

.ivu-mt, .ivu-mt-16 {
    margin-top: 16px !important
}

.ivu-mr, .ivu-mr-16 {
    margin-right: 16px !important
}

.ivu-mb, .ivu-mb-16 {
    margin-bottom: 16px !important
}

.ivu-ml, .ivu-ml-16 {
    margin-left: 16px !important
}

.ivu-p, .ivu-p-16 {
    padding: 16px !important
}

.ivu-pt, .ivu-pt-16 {
    padding-top: 16px !important
}

.ivu-pr, .ivu-pr-16 {
    padding-right: 16px !important
}

.ivu-pb, .ivu-pb-16 {
    padding-bottom: 16px !important
}

.ivu-pl, .ivu-pl-16 {
    padding-left: 16px !important
}

.ivu-anim-fade-appear, .ivu-anim-fade-enter-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-fade-leave-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-fade-appear, .ivu-anim-fade-enter-active {
    -webkit-animation-name: ivuFadeIn;
    animation-name: ivuFadeIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-fade-leave-active {
    -webkit-animation-name: ivuFadeOut;
    animation-name: ivuFadeOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-fade-appear, .ivu-anim-fade-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear
}

.ivu-anim-fade-leave-active {
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear
}

.ivu-anim-move-up-appear, .ivu-anim-move-up-enter-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-move-up-leave-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-move-up-appear, .ivu-anim-move-up-enter-active {
    -webkit-animation-name: ivuMoveUpIn;
    animation-name: ivuMoveUpIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-move-up-leave-active {
    -webkit-animation-name: ivuMoveUpOut;
    animation-name: ivuMoveUpOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-move-up-appear, .ivu-anim-move-up-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-move-up-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-move-down-appear, .ivu-anim-move-down-enter-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-move-down-leave-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-move-down-appear, .ivu-anim-move-down-enter-active {
    -webkit-animation-name: ivuMoveDownIn;
    animation-name: ivuMoveDownIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-move-down-leave-active {
    -webkit-animation-name: ivuMoveDownOut;
    animation-name: ivuMoveDownOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-move-down-appear, .ivu-anim-move-down-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-move-down-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-move-left-appear, .ivu-anim-move-left-enter-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-move-left-leave-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-move-left-appear, .ivu-anim-move-left-enter-active {
    -webkit-animation-name: ivuMoveLeftIn;
    animation-name: ivuMoveLeftIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-move-left-leave-active {
    -webkit-animation-name: ivuMoveLeftOut;
    animation-name: ivuMoveLeftOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-move-left-appear, .ivu-anim-move-left-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-move-left-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-move-right-appear, .ivu-anim-move-right-enter-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-move-right-leave-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-move-right-appear, .ivu-anim-move-right-enter-active {
    -webkit-animation-name: ivuMoveRightIn;
    animation-name: ivuMoveRightIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-move-right-leave-active {
    -webkit-animation-name: ivuMoveRightOut;
    animation-name: ivuMoveRightOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-move-right-appear, .ivu-anim-move-right-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-move-right-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-ease-appear, .ivu-anim-ease-enter-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-ease-leave-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-ease-appear, .ivu-anim-ease-enter-active {
    -webkit-animation-name: ivuEaseIn;
    animation-name: ivuEaseIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-ease-leave-active {
    -webkit-animation-name: ivuEaseOut;
    animation-name: ivuEaseOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-ease-appear, .ivu-anim-ease-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-duration: .2s;
    animation-duration: .2s
}

.ivu-anim-ease-leave-active {
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-duration: .2s;
    animation-duration: .2s
}

.ivu-anim-transition-drop-appear, .ivu-anim-transition-drop-enter-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-transition-drop-leave-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-transition-drop-appear, .ivu-anim-transition-drop-enter-active {
    -webkit-animation-name: ivuTransitionDropIn;
    animation-name: ivuTransitionDropIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-transition-drop-leave-active {
    -webkit-animation-name: ivuTransitionDropOut;
    animation-name: ivuTransitionDropOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-transition-drop-appear, .ivu-anim-transition-drop-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-transition-drop-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-slide-up-appear, .ivu-anim-slide-up-enter-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-slide-up-leave-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-slide-up-appear, .ivu-anim-slide-up-enter-active {
    -webkit-animation-name: ivuSlideUpIn;
    animation-name: ivuSlideUpIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-slide-up-leave-active {
    -webkit-animation-name: ivuSlideUpOut;
    animation-name: ivuSlideUpOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-slide-up-appear, .ivu-anim-slide-up-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-slide-up-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-slide-down-appear, .ivu-anim-slide-down-enter-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-slide-down-leave-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-slide-down-appear, .ivu-anim-slide-down-enter-active {
    -webkit-animation-name: ivuSlideDownIn;
    animation-name: ivuSlideDownIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-slide-down-leave-active {
    -webkit-animation-name: ivuSlideDownOut;
    animation-name: ivuSlideDownOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-slide-down-appear, .ivu-anim-slide-down-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-slide-down-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-slide-left-appear, .ivu-anim-slide-left-enter-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-slide-left-leave-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-slide-left-appear, .ivu-anim-slide-left-enter-active {
    -webkit-animation-name: ivuSlideLeftIn;
    animation-name: ivuSlideLeftIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-slide-left-leave-active {
    -webkit-animation-name: ivuSlideLeftOut;
    animation-name: ivuSlideLeftOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-slide-left-appear, .ivu-anim-slide-left-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-slide-left-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-slide-right-appear, .ivu-anim-slide-right-enter-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-slide-right-leave-active {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.ivu-anim-slide-right-appear, .ivu-anim-slide-right-enter-active {
    -webkit-animation-name: ivuSlideRightIn;
    animation-name: ivuSlideRightIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-slide-right-leave-active {
    -webkit-animation-name: ivuSlideRightOut;
    animation-name: ivuSlideRightOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.ivu-anim-slide-right-appear, .ivu-anim-slide-right-enter-active {
    opacity: 0;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-slide-right-leave-active {
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.ivu-anim-loop {
    -webkit-animation: ani-load-loop 1s linear infinite;
    animation: ani-load-loop 1s linear infinite
}

.ivu-auth-prevent {
    display: inline-block;
    cursor: pointer
}

.ivu-auth-prevent-no-match {
    pointer-events: none
}

.ivu-avatar-list {
    display: inline-block
}

.ivu-avatar-list-item {
    display: inline-block;
    margin-left: -8px;
    cursor: pointer
}

.ivu-avatar-list-item:first-child {
    margin-left: 0
}

.ivu-avatar-list-item .ivu-avatar {
    border: 1px solid #fff
}

.ivu-avatar-list-item-excess {
    cursor: auto
}

.ivu-avatar-list-large .ivu-avatar-list-item {
    margin-left: -16px
}

.ivu-avatar-list-large .ivu-avatar-list-item:first-child {
    margin-left: 0
}

.ivu-avatar-list-large .ivu-avatar-list-item-excess {
    font-size: 16px
}

.ivu-avatar-list-default .ivu-avatar-list-item {
    margin-left: -12px
}

.ivu-avatar-list-default .ivu-avatar-list-item:first-child {
    margin-left: 0
}

.ivu-calendar-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 12px;
    border-bottom: 1px solid #e8eaec
}

.ivu-calendar-header-title {
    color: #17233d;
    font-size: 18px
}

.ivu-calendar-table {
    table-layout: fixed;
    width: 100%
}

.ivu-calendar-table thead th {
    padding: 24px 0 6px 6px;
    text-align: left;
    font-size: 14px
}

.ivu-calendar-table td {
    font-size: 14px;
    border-bottom: 1px solid #e8eaec;
    border-right: 1px solid #e8eaec;
    vertical-align: top
}

.ivu-calendar-table tr:first-child td {
    border-top: 1px solid #e8eaec
}

.ivu-calendar-table tr td:first-child {
    border-left: 1px solid #e8eaec
}

.ivu-calendar-table-day {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100px;
    padding: 6px;
    -webkit-transition: background-color .2s ease-in-out;
    transition: background-color .2s ease-in-out;
    cursor: pointer
}

.ivu-calendar-table-day:hover {
    background-color: #f0faff
}

.ivu-calendar-table-day-other .ivu-calendar-table-day-title {
    color: #c5c8ce
}

.ivu-calendar-table-day-current {
    background-color: #f0faff
}

.ivu-calendar-table-day-current .ivu-calendar-table-day-title {
    color: #2d8cf0
}

.ivu-calendar-table-year {
    padding-top: 24px
}

.ivu-city {
    display: inline-block;
    width: 100%;
    position: relative
}

.ivu-city .ivu-dropdown {
    width: 100%
}

.ivu-city-rel {
    display: inline-block;
    width: 100%;
    position: relative;
    cursor: pointer
}

.ivu-city-arrow {
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-city-visible .ivu-city-arrow:nth-of-type(2) {
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg)
}

.ivu-city .ivu-select-dropdown {
    width: 400px
}

.ivu-city-drop {
    width: 400px;
    padding: 2px 8px
}

.ivu-city-drop-cities {
    margin-bottom: 8px
}

.ivu-city-drop-cities span {
    display: inline-block;
    margin-right: 4px;
    cursor: pointer
}

.ivu-city-drop-cities span:hover {
    color: #57a3f3
}

.ivu-city-drop-menu {
    margin-bottom: 8px
}

.ivu-city-drop-type {
    display: inline-block
}

.ivu-city-drop-search {
    display: inline-block;
    margin-left: 8px
}

.ivu-city-drop-list-letter {
    margin-bottom: 8px
}

.ivu-city-drop-list-letter .ivu-tag {
    cursor: pointer
}

.ivu-city-drop-list-letter .ivu-tag:hover .ivu-tag-text {
    color: #57a3f3
}

.ivu-city-drop-list-main {
    max-height: 200px;
    overflow: auto
}

.ivu-city-drop-list-main dt {
    float: left;
    font-weight: 700
}

.ivu-city-drop-list-main dd {
    white-space: normal;
    -webkit-margin-start: 40px;
    margin-inline-start: 40px;
    margin-bottom: 8px
}

.ivu-city-drop-list-main dd li {
    display: inline-block;
    margin-right: 9px;
    cursor: pointer
}

.ivu-city-drop-list-main dd li:hover {
    color: #57a3f3
}

.ivu-city-drop-list-main-city dd {
    -webkit-margin-start: 24px;
    margin-inline-start: 24px
}

.ivu-city-transfer.ivu-select-dropdown {
    max-height: none;
    overflow: visible
}

.ivu-description-list-title {
    margin-bottom: 16px;
    color: #17233d;
    font-weight: 500;
    font-size: 14px
}

.ivu-description-term {
    display: table-cell;
    padding-bottom: 16px;
    color: #17233d;
    line-height: 20px;
    white-space: nowrap
}

.ivu-description-detail {
    display: table-cell;
    width: 100%;
    padding-bottom: 16px;
    color: #515a6e;
    line-height: 20px
}

.ivu-description-list-vertical .ivu-description-term {
    display: block;
    padding-bottom: 8px
}

.ivu-description-list-vertical .ivu-description-detail {
    display: block
}

.ivu-ellipsis-hidden {
    visibility: hidden
}

.ivu-exception {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 80%;
    min-height: 500px
}

.ivu-exception-img {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 62.5%;
    flex: 0 0 62.5%;
    width: 62.5%;
    padding-right: 152px;
    zoom: 1
}

.ivu-exception-img::after, .ivu-exception-img::before {
    content: ' ';
    display: table
}

.ivu-exception-img::after {
    clear: both;
    height: 0;
    font-size: 0;
    visibility: hidden
}

.ivu-exception-img-element {
    float: right;
    width: 100%;
    max-width: 430px;
    height: 360px;
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: contain
}

.ivu-exception-content {
    -webkit-box-flex: 1;
    -ms-flex: auto;
    flex: auto
}

.ivu-exception-content h1 {
    margin-bottom: 24px;
    color: #515a6e;
    font-weight: 600;
    font-size: 72px;
    line-height: 72px
}

.ivu-exception-content-desc {
    margin-bottom: 16px;
    color: #808695;
    font-size: 20px;
    line-height: 28px
}

.ivu-exception-content-actions button:not(:last-child) {
    margin-right: 8px
}

@media screen and (max-width: 768px) {
    .ivu-exception-img {
        padding-right: 88px
    }
}

@media screen and (max-width: 576px) {
    .ivu-exception {
        display: block;
        text-align: center
    }

    .ivu-exception-img {
        margin: 0 auto 24px;
        padding-right: 0
    }
}

@media screen and (max-width: 480px) {
    .ivu-exception-img {
        margin-bottom: -24px;
        overflow: hidden
    }
}

.ivu-footer-toolbar {
    position: fixed;
    right: 0;
    bottom: 0;
    z-index: 9;
    width: 100%;
    height: 56px;
    padding: 0 24px;
    line-height: 56px;
    background: #fff;
    border-top: 1px solid #e8eaec;
    -webkit-box-shadow: 0 -1px 2px rgba(0, 0, 0, .03);
    box-shadow: 0 -1px 2px rgba(0, 0, 0, .03)
}

.ivu-footer-toolbar::after {
    display: block;
    clear: both;
    content: ''
}

.ivu-footer-toolbar-left {
    float: left
}

.ivu-footer-toolbar-right {
    float: right
}

.ivu-footer-toolbar button + button {
    margin-left: 8px
}

.ivu-global-footer {
    /*margin: 48px 0 24px 0;*/
    /*padding: 0 16px;*/
    margin: 15px 0px;
    text-align: center;
    box-sizing: border-box;
}

.ivu-global-footer-links {
    margin-bottom: 8px
}

.ivu-global-footer-links a {
    font-size: 14px;
    color: #808695;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.ivu-global-footer-links a:not(:last-child) {
    margin-right: 40px
}

.ivu-global-footer-links a:hover {
    color: #515a6e
}

.ivu-global-footer-copyright {
    color: #808695;
    font-size: 14px
}

.ivu-grid:after {
    content: '';
    display: block;
    clear: both
}

.ivu-grid-item {
    position: relative;
    float: left;
    width: 33.33%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: 0;
    border-radius: 0;
    -webkit-transition: -webkit-box-shadow .2s ease-in-out;
    transition: -webkit-box-shadow .2s ease-in-out;
    transition: box-shadow .2s ease-in-out;
    transition: box-shadow .2s ease-in-out, -webkit-box-shadow .2s ease-in-out
}

.ivu-grid-item-main {
    padding: 24px
}

.ivu-grid-border .ivu-grid-item {
    -webkit-box-shadow: 1px 0 0 0 #e8eaec, 0 1px 0 0 #e8eaec, 1px 1px 0 0 #e8eaec, 1px 0 0 0 #e8eaec inset, 0 1px 0 0 #e8eaec inset;
    box-shadow: 1px 0 0 0 #e8eaec, 0 1px 0 0 #e8eaec, 1px 1px 0 0 #e8eaec, 1px 0 0 0 #e8eaec inset, 0 1px 0 0 #e8eaec inset
}

.ivu-grid-hover .ivu-grid-item:hover {
    z-index: 1;
    -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 6px rgba(0, 0, 0, .2)
}

.ivu-grid-center .ivu-grid-item-main {
    width: 100%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
    transform: translate(0, -50%);
    text-align: center
}

.ivu-notifications {
    display: inline-block
}

.ivu-notifications-rel {
    display: inline-block;
    cursor: pointer
}

.ivu-notifications-list {
    width: 300px;
    line-height: normal
}

.ivu-notifications-list-wide {
    width: auto
}

.ivu-notifications .ivu-tabs-nav-scroll {
    text-align: center
}

.ivu-notifications .ivu-tabs-nav {
    display: inline-block;
    float: none
}

.ivu-notifications-tabs .ivu-badge {
    margin-left: 3px
}

.ivu-notifications-tabs .ivu-badge-count {
    background: #e6ebf1;
    color: #808695;
    min-width: 16px;
    height: 16px;
    line-height: 14px;
    border-radius: 8px;
    padding: 0 4px
}

.ivu-notifications-tabs .ivu-tabs-tab:last-child {
    margin-right: 0
}

.ivu-notifications-tabs .ivu-tabs-bar {
    margin-bottom: 0
}

.ivu-notifications-extra {
    border-top: 1px solid #e8eaec
}

.ivu-notifications-tab-empty {
    text-align: center;
    padding: 64px 0
}

.ivu-notifications-tab-empty-img {
    display: inline-block;
    height: 64px
}

.ivu-notifications-tab-empty-text {
    color: #808695
}

.ivu-notifications-tab-clear {
    border-top: 1px solid #e8eaec;
    text-align: center;
    font-size: 14px;
    cursor: pointer;
    padding: 6px 0;
    color: #515a6e;
    -webkit-transition: color .2s ease-in-out;
    transition: color .2s ease-in-out
}

.ivu-notifications-tab-clear:hover {
    color: #57a3f3
}

.ivu-notifications-tab-loading-item {
    border-top: 1px solid #e8eaec;
    text-align: center;
    font-size: 14px;
    padding: 6px 0;
    color: #515a6e;
    -webkit-transition: color .2s ease-in-out;
    transition: color .2s ease-in-out
}

.ivu-notifications-tab-loading-all, .ivu-notifications-tab-loading-show {
    color: #c5c8ce
}

.ivu-notifications-tab-loading-more {
    cursor: pointer;
    color: #2d8cf0
}

.ivu-notifications-tab-loading-more:hover {
    color: #57a3f3
}

.ivu-notifications-container {
    max-height: 400px;
    overflow: auto
}

.ivu-notifications-item {
    padding: 12px 24px;
    border-bottom: 1px solid #e8eaec;
    cursor: pointer;
    -webkit-transition: background-color .2s ease-in-out;
    transition: background-color .2s ease-in-out;
    text-align: left
}

.ivu-notifications-item:last-child {
    border-bottom: none
}

.ivu-notifications-item:hover {
    background-color: #f0faff
}

.ivu-notifications-item-unread {
    background-color: #f8f8f9
}

.ivu-notifications-item-title {
    margin-bottom: 4px
}

.ivu-notifications-item-title h4 {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #515a6e
}

.ivu-notifications-item-tag {
    float: right;
    margin-top: -2px
}

.ivu-notifications-item-tag .ivu-tag {
    margin-right: 0
}

.ivu-notifications-item-desc {
    color: #808695;
    font-size: 12px;
    margin-bottom: 4px
}

.ivu-notifications-item-time {
    font-size: 12px;
    color: #808695
}

.ivu-number-info-title {
    margin-bottom: 16px;
    color: #17233d;
    font-size: 16px;
    -webkit-transition: all .2s;
    transition: all .2s
}

.ivu-number-info-subTitle {
    height: 22px;
    overflow: hidden;
    color: #808695;
    font-size: 14px;
    line-height: 22px;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all
}

.ivu-number-info-value {
    margin-top: 4px;
    overflow: hidden;
    font-size: 0;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all
}

.ivu-number-info-total {
    display: inline-block;
    height: 32px;
    margin-right: 32px;
    color: #515a6e;
    font-size: 24px;
    line-height: 32px
}

.ivu-number-info-subTotal {
    display: inline-block;
    height: 32px;
    line-height: 32px;
    margin-right: 0;
    vertical-align: top;
    font-size: 14px;
    color: #808695
}

.ivu-page-header {
    padding: 16px 32px 0 32px;
    background: #fff;
    border-bottom: 1px solid #e8eaec
}

.ivu-page-header-wide {
    max-width: 1200px;
    margin: auto
}

.ivu-page-header-detail {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.ivu-page-header-row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%
}

.ivu-page-header-breadcrumb {
    margin-bottom: 16px
}

.ivu-page-header-tabs {
    margin: 0 0 0 -8px
}

.ivu-page-header-tabs .ivu-tabs-bar {
    margin-bottom: 0;
    border-bottom-color: transparent
}

.ivu-page-header-logo {
    -webkit-box-flex: 0;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto;
    margin-right: 16px;
    padding-top: 1px
}

.ivu-page-header-logo > img {
    display: block;
    width: 28px;
    height: 28px;
    border-radius: 4px
}

.ivu-page-header-back {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 30px;
    -webkit-box-flex: 0;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto;
    margin-right: 8px;
    cursor: pointer;
    -webkit-transition: color .2s ease-in-out;
    transition: color .2s ease-in-out
}

.ivu-page-header-back:hover {
    color: #2d8cf0
}

.ivu-page-header-back .ivu-icon {
    font-size: 20px;
    line-height: inherit
}

.ivu-page-header-back .ivu-divider {
    height: 14px;
    line-height: inherit;
    top: 8px
}

.ivu-page-header-main .ivu-page-header-back {
    display: none
}

.ivu-page-header-title {
    display: inline-block;
    color: #17233d;
    font-weight: 500;
    font-size: 20px
}

.ivu-page-header-action {
    min-width: 266px;
    margin-left: 56px
}

.ivu-page-header-action .ivu-btn-group:not(:last-child), .ivu-page-header-action .ivu-btn:not(:last-child) {
    margin-right: 8px
}

.ivu-page-header-action .ivu-btn-group > .ivu-btn {
    margin-right: 0
}

.ivu-page-header-content, .ivu-page-header-extra {
    font-size: 14px
}

.ivu-page-header-content, .ivu-page-header-title {
    -webkit-box-flex: 1;
    -ms-flex: auto;
    flex: auto
}

.ivu-page-header-action, .ivu-page-header-extra, .ivu-page-header-main {
    -webkit-box-flex: 0;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto
}

.ivu-page-header-main {
    width: 100%
}

.ivu-page-header-action, .ivu-page-header-title {
    margin-bottom: 16px
}

.ivu-page-header-content, .ivu-page-header-extra, .ivu-page-header-logo {
    margin-bottom: 16px
}

.ivu-page-header-action, .ivu-page-header-extra {
    text-align: right
}

.ivu-page-header-extra {
    min-width: 242px;
    margin-left: 88px
}

@media screen and (max-width: 1200px) {
    .ivu-page-header-extra {
        margin-left: 44px
    }
}

@media screen and (max-width: 992px) {
    .ivu-page-header-extra {
        margin-left: 20px
    }
}

@media screen and (max-width: 768px) {
    .ivu-page-header-row {
        display: block
    }

    .ivu-page-header-action, .ivu-page-header-extra {
        margin-left: 0;
        text-align: left
    }
}

@media screen and (max-width: 576px) {
    .ivu-page-header-detail > .ivu-page-header-back {
        display: none
    }

    .ivu-page-header-main .ivu-page-header-back {
        display: inline-block;
        position: relative;
        top: -4px
    }

    .ivu-page-header-main .ivu-page-header-back .ivu-divider {
        top: 0
    }

    .ivu-page-header-detail {
        display: block
    }
}

@media screen and (max-width: 480px) {
    .ivu-page-header-action .ivu-btn, .ivu-page-header-action .ivu-btn-group {
        display: block;
        margin-bottom: 8px
    }

    .ivu-page-header-action .ivu-btn-group > .ivu-btn {
        display: inline-block;
        margin-bottom: 0
    }
}

.ivu-result {
    width: 72%;
    margin: 0 auto;
    text-align: center
}

@media screen and (max-width: 480px) {
    .ivu-result {
        width: 100%
    }
}

.ivu-result-icon {
    display: inline-block;
    width: 72px;
    border-radius: 50%;
    margin-bottom: 24px
}

.ivu-result-icon-success {
    background-color: #19be6b
}

.ivu-result-icon-error {
    background-color: #ed4014
}

.ivu-result-icon .ivu-icon {
    color: #fff;
    font-size: 72px;
    border-radius: 50%
}

.ivu-result-title {
    margin-bottom: 16px;
    color: #17233d;
    font-weight: 500;
    font-size: 24px;
    line-height: 32px
}

.ivu-result-desc {
    margin-bottom: 24px;
    color: #808695;
    font-size: 14px;
    line-height: 22px
}

.ivu-result-extra {
    padding: 24px 40px;
    text-align: left;
    background: #f8f8f9;
    border-radius: 4px
}

@media screen and (max-width: 480px) {
    .ivu-result-extra {
        padding: 18px 20px
    }
}

.ivu-result-actions {
    margin-top: 32px
}

.ivu-result-actions .ivu-btn:not(:last-child) {
    margin-right: 8px
}

.ivu-tag-select {
    position: relative;
    max-height: 32px;
    margin-left: -8px;
    overflow: hidden;
    line-height: 32px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.ivu-tag-select-expanded {
    max-height: 200px
}

.ivu-tag-select-option {
    display: inline-block
}

.ivu-tag-select-option .ivu-tag {
    margin-right: 24px
}

.ivu-tag-select-expand-btn {
    position: absolute;
    top: 1px;
    right: 0
}

.ivu-tag-select-with-expanded {
    padding-right: 50px
}

.ivu-tree-select .ivu-select-dropdown {
    padding: 0
}

.ivu-tree-select .ivu-tree, .ivu-tree-select-transfer .ivu-tree {
    padding: 0 6px
}

.ivu-tree-select .ivu-tree li, .ivu-tree-select-transfer .ivu-tree li {
    font-size: 14px
}

.ivu-tree-select .ivu-tree-title, .ivu-tree-select-transfer .ivu-tree-title {
    display: inline-block;
    width: calc(100% - 18px);
    vertical-align: middle
}

.ivu-tree-select .ivu-checkbox-wrapper + .ivu-tree-title, .ivu-tree-select-transfer .ivu-checkbox-wrapper + .ivu-tree-title {
    width: calc(100% - 44px)
}

.ivu-tree-select .ivu-tree-title {
    padding: 2px 4px 4px
}

.ivu-tree-select-transfer {
    padding: 0
}

.ivu-tree-select-transfer .ivu-tree-title {
    padding: 0 4px 2px
}

.ivu-trend {
    display: inline-block
}

.ivu-trend-text {
    vertical-align: middle
}

.ivu-trend-colorful.ivu-trend-up .ivu-trend-flag {
    color: #ed4014
}

.ivu-trend-colorful.ivu-trend-down .ivu-trend-flag {
    color: #19be6b
}

.ivu-trend-reverse-color.ivu-trend-colorful.ivu-trend-up .ivu-trend-flag {
    color: #19be6b
}

.ivu-trend-reverse-color.ivu-trend-colorful.ivu-trend-down .ivu-trend-flag {
    color: #ed4014
}

.ivu-trend-colorful.ivu-trend-text-color.ivu-trend-up .ivu-trend-text {
    color: #ed4014
}

.ivu-trend-colorful.ivu-trend-text-color.ivu-trend-down .ivu-trend-text {
    color: #19be6b
}

.ivu-trend-reverse-color.ivu-trend-colorful.ivu-trend-text-color.ivu-trend-up .ivu-trend-text {
    color: #19be6b
}

.ivu-trend-reverse-color.ivu-trend-colorful.ivu-trend-text-color.ivu-trend-down .ivu-trend-text {
    color: #ed4014
}

.ivu-word-count {
    display: inline-block
}

.ivu-word-count-overflow {
    color: #ed4014
}