<template>
    <div class="article-manager">
        <div class="i-layout-page-header">
            <PageHeader title="商品分类" hidden-breadcrumb></PageHeader>
        </div>
        <Card :bordered="false" dis-hover class="ivu-mt">
            <Form
                ref="artFrom"
                :model="artFrom"
                :label-width="labelWidth"
                :label-position="labelPosition"
                @submit.native.prevent
            >
                <Row type="flex" :gutter="24">
                    <Col v-bind="grid">
                        <FormItem label="状态：" label-for="is_show">
                            <Select
                                v-model="artFrom.is_show"
                                placeholder="请选择"
                                clearable
                                @on-change="userSearchs"
                            >
                                <Option value="10">显示</Option>
                                <Option value="1">隐藏</Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col v-bind="grid">
                        <FormItem label="分类名称：" label-for="status2">
                            <Input
                                search
                                enter-button
                                placeholder="请输入"
                                v-model="artFrom.cate_name"
                                @on-search="userSearchs"
                            />
                        </FormItem>
                    </Col>
                </Row>
                <Row type="flex">
                     <Col v-bind="grid">
                         <Button type="primary"  icon="md-add" @click="add">添加分类</Button>
                    </Col>
                </Row>
            </Form>
            <vxe-table
                class="mt25"
                highlight-hover-row
                :loading="loading"
                header-row-class-name="false"
                :data="tableData"
            >
                <vxe-table-column field="id" title="ID" tooltip width="190"></vxe-table-column>
                <vxe-table-column field="shop_name" title="店铺名称" min-width="120"></vxe-table-column>
                <vxe-table-column field="name" title="分类名称" min-width="120"></vxe-table-column>
                <vxe-table-column field="img" title="分类图标" min-width="100">
                    <template v-slot="{ row }">
                        <viewer>
                            <div class="tabBox_img">
                                <img v-lazy="row.img" />
                            </div>
                        </viewer>
                    </template>
                </vxe-table-column>
                <vxe-table-column field="sort" title="排序" min-width="20" tooltip="true"></vxe-table-column>
                <vxe-table-column field="is_show" title="状态" min-width="80">
                    <template v-slot="{ row }">
                        <i-switch
                            v-model="row.is_show"
                            :value="row.is_show"
                            :true-value="0"
                            :false-value="1"
                            @on-change="onchangeIsShow(row)"
                            size="large"
                        >
                            <span slot="open">显示</span>
                            <span slot="close">隐藏</span>
                        </i-switch>
                    </template>
                </vxe-table-column>
                <vxe-table-column field="date" title="操作" width="250" fixed="right" align="center">
                    <template v-slot="{ row, index }">
                        <a @click="edit(row)">编辑</a>
                        <Divider type="vertical" />
                        <a @click="del(row, '删除商品分类', index)">删除</a>
                    </template>
                </vxe-table-column>
            </vxe-table>
        </Card>
        <!-- 添加 编辑表单-->
        <add-form ref="template"></add-form>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import { categoryListApi, categoryDelApi, setCategoryApi, shopListApi } from '@/api/coffee';
import addForm from "./addForm";
export default {
    name: 'product_productClassify',
    components: {
        addForm
    },
    data() {
        return {
            // 店铺列表
            shopList: [],
            FromData: null,
            grid: {
                xl: 7,
                lg: 7,
                md: 12,
                sm: 24,
                xs: 24
            },
            loading: false,
            artFrom: {
                pid: 0,
                is_show: '',
                shop_id:'',
                page: 1,
                cate_name: '',
                limit: 15
            },
            total: 0,
            tableData: []
        }
    },
    computed: {
        ...mapState('admin/layout', [
            'isMobile'
        ]),
        ...mapState('admin/userLevel', [
            'categoryId'
        ]),
        labelWidth() {
            return this.isMobile ? undefined : 75;
        },
        labelPosition() {
            return this.isMobile ? 'top' : 'right';
        }
    },
    mounted() {
        this.getList();
        shopListApi().then(res => {
            this.shopList = res.data.list
        })
    },
    methods: {
        // 列表
        getList() {
            this.loading = true;
            this.artFrom.is_show = this.artFrom.is_show || '';
            this.artFrom.shop_id = this.$store.state.admin.user.info.shop_id;
            this.artFrom.pid = this.artFrom.pid || '';
            categoryListApi(this.artFrom).then(async res => {
                let data = res.data
                this.tableData = data.list;
                this.total = data.count;
                this.loading = false;
            }).catch(res => {
                this.loading = false;
                this.$Message.error(res.msg);
            })
        },
        pageChange(index) {
            this.artFrom.page = index;
            this.getList();
        },
        // 添加
        add() {
            this.$refs.template.id = 0;
            this.$refs.template.isTemplate = true;
        },
        // 编辑
        edit(row) {
			this.$refs.template.title = '编辑分类'
			this.$refs.template.isTemplate = true;
            this.$refs.template.getShop();
			this.$refs.template.getInfo(row.id);
        },
        // 修改状态
        onchangeIsShow(row) {
            let data = {
                id: row.id,
                is_show: row.is_show
            }
            setCategoryApi(data).then(async res => {
                this.$Message.success(res.msg);
            }).catch(res => {
                this.$Message.error(res.msg);
            })
        },
        // 下拉树
        handleCheckChange(data) {
            let value = ''
            let title = ''
            this.list = []
            this.artFrom.pid = 0;
            data.forEach((item, index) => {
                value += `${item.id},`
                title += `${item.title},`
            });
            value = value.substring(0, value.length - 1)
            title = title.substring(0, title.length - 1)
            this.list.push({
                value,
                title
            });
            this.artFrom.pid = value;
            this.getList();
        },
        // 删除
        del(row, tit, num) {
			this.$Modal.confirm({
				title: '确定要删除该分类吗？',
				content: '删除该分类后将无法恢复，请谨慎操作！',
				loading: true,
				onOk: () => {
					categoryDelApi({'id':row.id}).then(res=>{
						this.$Message.success(res.msg);
						this.getList()
						this.$Modal.remove();
					})
				}
			});
        },
        // 表格搜索
        userSearchs() {
            this.artFrom.page = 1;
            this.getList();
        }
    }
}
</script>
<style scoped lang="stylus">
    .treeSel >>>.ivu-select-dropdown-list
        padding 0 10px!important
        box-sizing border-box
    .tabBox_img
        width 36px
        height 36px
        border-radius:4px
        cursor pointer
        img
            width 100%
            height 100%
    >>>.ivu-select,.ivu-select-item,.vxe-table
        font-size 12px !important
    /deep/.ivu-input
        font-size 14px !important
</style>

<style>
/*.ivu-input{*/
/*    font-size: 14px !important;*/
/*}*/
</style>
