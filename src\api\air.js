// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import request from '@/plugins/request';

/**
 * @description 用户管理--列表
 * @param {Object} param data {Object} 传值参数
 */
export function userList(data) {
    return request({
        url: 'coffee/user/list',
        method: 'get',
        params: data
    });
}

/**
 * @description 用户管理--列表
 * @param {Object} param data {Object} 传值参数
 */
export function getUserData(data) {
    return request({
        url: `coffee/user/edit`,
        method: 'get',
        params: data
    });
}

/**
 * @description 会员管理-详情
 * @param {Number} param id {Number} 用户id
 */
export function detailsApi(data) {
    return request({
        url: `coffee/user/detail`,
        method: 'get',
        params: data
    });
}

/**
 * @description 会员管理详情中tab选项
 * @param {Number} param id {Number} 用户id
 */
export function infoApi(params) {
    return request({
        url: `coffee/user/one_info`,
        method: 'post',
        params
    });
}

/**
 * @description 用户管理--列表
 * @param {Object} param data {Object} 传值参数
 */
export function editOtherApi(data) {
    return request({
        url: `coffee/user/edit_other`,
        method: 'get',
        params: data
    });
}

