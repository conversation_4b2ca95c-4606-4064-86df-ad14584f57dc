// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import BasicLayout from '@/layouts/basic-layout';

const pre = 'coffee_product_';

export default {
    path: '/admin/coffee_product',
    name: 'coffee_product',
    header: 'coffee_product',
    meta: {
        // 授权标识
        auth: ['admin-store-index']
    },
    redirect: {
        name: `${pre}productList`
    },
    component: BasicLayout,
    children: [
        {
            path: 'product_list',
            name: `${pre}productList`,
            meta: {
                title: '商品管理',
                auth: ['admin-store-storeProuduct-index']
            },
            component: () => import('@/pages/coffeeProduct/productList')
        },
        {
            path: 'order_list',
            name: `${pre}orderList`,
            meta: {
                title: '订单管理',
                auth: ['admin-store-storeProuduct-index']
            },
            component: () => import('@/pages/coffeeProduct/orderList')
        },
        {
            path: 'add_product/:id?',
            name: `${pre}productAdd`,
            meta: {
                auth: ['admin-store-storeProuduct-index'],
                title: '商品添加'
            },
            component: () => import('@/pages/coffeeProduct/productAdd')
        }
    ]
};
