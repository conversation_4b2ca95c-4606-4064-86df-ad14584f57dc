<template>
  <div>
    <Table
      :columns="columns"
      :data="orderList"
      ref="table"
      :loading="loading"
      highlight-row
      no-data-text="暂无数据"
      no-filtered-data-text="暂无筛选结果"
      class="orderData mt25"
    >
      <template slot-scope="{ row, index }" slot="ordernum">
        <span v-text="row.ordernum" style="display: block"></span>
        <span v-show="row.is_del === 1" style="color: #ed4014; display: block"
          >用户已删除</span
        >
      </template>
      <template slot-scope="{ row, index }" slot="type">
        <span style="display: block" v-if="row.coupon_id > 0">
          优惠券支付
        </span>
        <span style="display: block" v-else-if="row.type == 'offline'">
          园区卡支付
        </span>
        <span style="display: block" v-else-if="row.type == 'parkCard'">
          电子园区卡支付
        </span>
        <span style="display: block" v-else-if="row.type == 'weixin'">
          微信支付
        </span>
        <span style="display: block" v-else-if="row.type == 'yue'">
          余额支付
        </span>
        <span style="display: block" v-else-if="row.type == 'credit'">
          挂账
        </span>
        <span style="display: block" v-else="row.type == 'weixin'">
          {{ row.type }}
        </span>

      </template>
      <template slot-scope="{ row, index }" slot="nickname">
        {{ row.nickname }}
      </template>
      <template slot-scope="{ row, index }" slot="info">
        <div class="tabBox">
          <div class="tabBox_img" v-viewer>
            <img v-lazy="row.info.img" />
          </div>
          <span class="tabBox_tit">
            {{ row.info.name + " | " }}{{ row.product_attr }}
          </span>
          <span class="tabBox_pice">{{
            "￥" + row.price + " x " + row.num
          }}</span>
        </div>
      </template>
      <template slot-scope="{ row, index }" slot="invalid">
        <span v-show="row.invalid_shop === 0" style="display: block">未作废</span>
        <span v-show="row.invalid_shop === 1" style="color: #ed4014; display: block"
          >已作废</span
        >
      </template>
      
      <template slot-scope="{ row, index }" slot="action">
        <a @click="edit(row)" v-if="row.status !== '购物车'">打印小票</a>
        <Divider type="vertical" v-if="row.status == '待核销'" />
        <a @click="wirte(row)" v-if="row.status == '待核销'">核销订单</a>
        <Divider type="vertical" />
        <a @click="invalid(row)" v-if="row.status !== '购物车'&&row.invalid_shop==0">作废</a>
        <a @click="invalid(row)" v-if="row.status !== '购物车'&&row.invalid_shop==1">不作废</a>
        <Divider type="vertical" />
        <a @click="updatePaypalType(row)" v-if="row.type == 'credit'">更改支付</a>
      </template>
    </Table>
    <Modal
        v-model="modalPayPalType"
        title="更改挂账支付类型">
        <RadioGroup v-model="updatePaypalName">
          <Radio label="园区卡支付"></Radio>
          <Radio label="POS扫码付款"></Radio>
          <Radio label="余额支付"></Radio>
      </RadioGroup>
      <div slot="footer">
        <Button type="success" size="large" long :loading="paypal_loading" @click="PayPalTypeSuccess">提交</Button>
      </div>
    </Modal>
    <div class="acea-row row-right page">
      <Page
        :total="page.total"
        :current="page.pageNum"
        show-elevator
        show-total
        @on-change="pageChange"
        :page-size="page.pageSize"
        @on-page-size-change="limitChange"
        show-sizer
      />
    </div>
  </div>
</template>

<script>
import expandRow from "./tableExpand.vue";
import {
  getDataInfo,
  getnoRefund,
  refundIntegral,
  writeUpdate,
} from "@/api/order";
import { orderList, printOrder, invalidOrder, wirteOrder,updatePayPalType } from "@/api/coffee";
import { mapState, mapMutations } from "vuex";

export default {
  name: "table_list",
  components: {
    expandRow,
  },
  props: ["where", "isAll"],
  data() {
    return {
      // 支付方式选择
      paypal_loading:false,
      modalPayPalType:false,
      // 待修改id
      updatePaypalTypeId:'',
      updatePaypalName:'园区卡支付',
      distshow: false, //分配的弹窗
      delfromData: {},
      modal: false,
      orderList: [],
      pay_type: "",
      orderCards: [],
      loading: false,
      orderId: 0,
      columns: [
        {
          type: "expand",
          width: 30,
          render: (h, params) => {
            return h(expandRow, {
              props: {
                row: params.row,
              },
            });
          },
        },
        {
          title: "店铺名称",
          align: "center",
          key: "shop_name",
          minWidth: 150,
        },
        {
          title: "订单号",
          align: "center",
          slot: "ordernum",
          minWidth: 150,
        },
        {
          title: "取餐号",
          key: "take_num",
          minWidth: 60,
        },
        {
          title: "订单类型",
          key: "type",
          slot: "type",
          minWidth: 120,
        },
        {
          title: "用户昵称",
          slot: "nickname",
          minWidth: 100,
        },
        {
          title: "用户手机号",
          key: "phone",
          minWidth: 100,
        },
        {
          title: "商品信息",
          slot: "info",
          minWidth: 330,
        },
        {
          title: "实际支付",
          key: "pay_price",
          minWidth: 70,
        },
        {
          title: "支付时间",
          key: "pay_time",
          minWidth: 100,
        },
        {
          title: "创建时间",
          key: "create_time",
          minWidth: 100,
        },
        {
          title: "订单状态",
          key: "status",
          minWidth: 100,
        },
        {
          title: "是否有效",
          slot: "invalid",
          minWidth: 100,
        },
        {
          title: "操作",
          slot: "action",
          fixed: "right",
          minWidth: 180,
          align: "center",
        },
      ],
      page: {
        total: 0, // 总条数
        pageNum: 1, // 当前页
        pageSize: 10, // 每页显示条数
      },
      data: [],
      FromData: null,
      orderDatalist: null,
      modalTitleSs: "",
      isDelIdList: [],
      checkBox: false,
      formSelection: [],
      selectionCopy: [],
      display: "none",
      autoDisabled: false,
      status: 0, //发货状态判断
    };
  },
  computed: {
    ...mapState("admin/order", [
      "orderPayType",
      "orderStatus",
      "orderTime",
      "orderNum",
      "fieldKey",
      "orderType",
      "getOrderSelf",
    ]),
  },
  mounted() {},
  created() {
    this.getList();
  },
  watch: {
    orderType: function() {
      this.page.pageNum = 1;
      this.getList();
    },
    formSelection(value) {
      this.$emit("order-select", value);
      if (value.length) {
        this.$emit("auto-disabled", 0);
      } else {
        this.$emit("auto-disabled", 1);
      }
      let isDel = value.some((item) => {
        return item.is_del === 1;
      });
      this.getIsDel(isDel);
      this.getisDelIdListl(value);
    },
    orderList: {
      deep: true,
      handler(value) {
        value.forEach((item) => {
          this.formSelection.forEach((itm) => {
            if (itm.id === item.id) {
              item.checkBox = true;
            }
          });
        });
        const arr = this.orderList.filter((item) => item.checkBox);
        if (this.orderList.length) {
          this.checkBox = this.orderList.length === arr.length;
        } else {
          this.checkBox = false;
        }
      },
    },
  },
  methods: {
    ...mapMutations("admin/order", ["getIsDel", "getisDelIdListl"]),
    // 核销方法
    wirte(row) {
      wirteOrder({ code: row.ordernum }).then((res) => {
        if (res.code === 200) {
          this.getList();
          this.$Message.success(res.msg);
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 挂账点击取消
    PayPalTypeCancel() {
      this.modalPayPalType = false
    },
    // 挂账点击确认
    PayPalTypeSuccess() {
      // 请求
      this.paypal_loading = true
      updatePayPalType({ 
        id: this.updatePaypalTypeId,
        type: this.updatePaypalName 
      }).then((res) => {
        if (res.code === 200) {
          this.getList();
          this.$Message.success(res.msg);
        } else {
          this.$message.error(res.msg);
        }
        this.paypal_loading = false
        this.modalPayPalType = false
      });
    },
    // 更改支付类型
    updatePaypalType(row){
      this.modalPayPalType = true
      this.updatePaypalTypeId = row.id
    },
    // 编辑
    edit(row) {
      printOrder({ id: row.id }).then((res) => {
        if (res.code === 200) {
          this.$Message.success(res.msg);
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 作废
    invalid(row) {
      invalidOrder({ id: row.id,invalid_shop: row.invalid_shop }).then((res) => {
        if (res.code === 200) {
          this.getList();
          this.$Message.success(res.msg);
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    distribution(row) {
      this.$refs.distshow.modals = true;
      this.$refs.distshow.getid(row.id);
    },
    showUserInfo(row) {
      this.$refs.userDetails.modals = true;
      this.$refs.userDetails.getDetails(row.uid);
    },
    // 立即支付 /确认收货//删除单条订单
    submitModel() {
      this.getList();
    },
    pageChange(index) {
      this.page.pageNum = index;
      this.getList();
    },
    limitChange(limit) {
      this.page.pageSize = limit;
      this.getList();
    },
    // 订单列表
    getList(res) {
      this.page.pageNum = res === 1 ? 1 : this.page.pageNum;
      this.loading = true;
      orderList({
        page: this.page.pageNum,
        limit: this.page.pageSize,
        status: this.orderStatus,
        pay_type: this.orderPayType,
        is_self: this.getOrderSelf,
        data: this.orderTime,
        real_name: this.orderNum,
        field_key: this.fieldKey,
        type: this.orderType === 0 ? "" : this.orderType,
      }).then((res) => {
        let data = res.data;
        this.orderList = data.data.map((item) => {
          if (this.isAll === 1) {
            item.checkBox = true;
          } else {
            item.checkBox = false;
          }
          return item;
        });
        this.orderCards = data.stat;
        this.page.total = data.count;
        this.$emit("on-changeCards", data.stat);
        this.loading = false;
      });
    },
    // 全选
    // 获取详情表单数据
    getData(id) {
      getDataInfo(id)
        .then(async (res) => {
          this.$refs.detailss.modals = true;
          this.orderDatalist = res.data;
          if (this.orderDatalist.orderInfo.refund_reason_wap_img) {
            try {
              this.orderDatalist.orderInfo.refund_reason_wap_img = JSON.parse(
                this.orderDatalist.orderInfo.refund_reason_wap_img
              );
            } catch (e) {
              this.orderDatalist.orderInfo.refund_reason_wap_img = [];
            }
          }
        })
        .catch((res) => {
          this.$Message.error(res.msg);
        });
    },
    // 获取退积分表单数据
    getRefundIntegral(id) {
      refundIntegral(id)
        .then(async (res) => {
          this.FromData = res.data;
          this.$refs.edits.modals = true;
        })
        .catch((res) => {
          this.$Message.error(res.msg);
        });
    },
    // 不退款表单数据
    getNoRefundData(id) {
      this.$modalForm(getnoRefund(id)).then(() => {
        this.getList();
        this.$emit("changeGetTabs");
      });
    },
    // 发送货
    sendOrder(row) {
      this.$store.commit("admin/order/setSplitOrder", row.total_num);
      this.$refs.send.modals = true;
      this.orderId = row.id;
      this.status = row._status;
      this.pay_type = row.pay_type;
      this.$refs.send.getList();
      this.$refs.send.getDeliveryList();
      this.$nextTick((e) => {
        this.$refs.send.getCartInfo(row._status, row.id);
      });
    },
    // 配送信息表单数据
    delivery(row) {
      getDistribution(row.id)
        .then(async (res) => {
          this.FromData = res.data;
          this.$refs.edits.modals = true;
        })
        .catch((res) => {
          this.$Message.error(res.msg);
        });
    },
    change(status) {},
    // 数据导出；
    exportData: function() {
      this.$refs.table.exportCsv({
        filename: "商品列表",
      });
    },
    // 核销订单
    bindWrite(row) {
      let self = this;
      this.$Modal.confirm({
        title: "提示",
        content: "确定要核销该订单吗？",
        cancelText: "取消",
        closable: true,
        maskClosable: true,
        onOk: function() {
          writeUpdate(row.order_id).then((res) => {
            self.$Message.success(res.msg);
            self.getList();
          });
        },
        onCancel: () => {},
      });
    },
  },
};
</script>

<style scoped lang="stylus">
img {
  height: 36px;
  display: block;
}

.tabBox {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;

  .tabBox_img {
    width: 36px;
    height: 36px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .tabBox_tit {
    width: 60%;
    font-size: 12px !important;
    margin: 0 2px 0 10px;
    letter-spacing: 1px;
    padding: 5px 0;
    box-sizing: border-box;
  }
}

.orderData >>>.ivu-table-cell {
  padding-left: 0 !important;
}

.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
}

.orderData .ivu-table {
  overflow: visible !important;
}

.orderData .ivu-table th {
  overflow: visible !important;
}

.orderData .ivu-table-header {
  overflow: visible !important;
}

/deep/.ivu-table-header {
  // overflow: visible;
}

/deep/.ivu-table th {
  overflow: visible;
}

/deep/.select-item:hover {
  background-color: #f3f3f3;
}

/deep/.select-on {
  display: block;
}

/deep/.select-item.on {
  /*background: #f3f3f3;*/
}
.pictrue-box {
  display: flex;
  align-item: center;
}

.pictrue {
  width: 25px;
  height: 25px;
}
</style>
